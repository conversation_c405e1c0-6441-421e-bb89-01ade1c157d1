---
### 割线与基准值：例
\item<+-> 考虑 $f(z) = \sqrt{z}$。支点 $0, \infty$。选割线为 $(-\infty, 0]$
\item<+-> 选择 $f(z = 1) = +1$
\begin{figure}
\includegraphics{branch-cut-and-reference-value.pdf}
</center>
\item<+-> 得到 $f(-1 + i \epsilon) = + i$，$f(- 1 - i \epsilon) = - i$，$\epsilon \to 0+$
---
### 割线：例
\item<+-> 更一般地，对 $x < 0$，割线的上岸和下岸的值已被确定，
$$
f(x + i \epsilon) = i |x|^{\frac{1}{2}}, \qquad
f(x - i \epsilon) = - i |x|^{\frac{1}{2}} \ .
$$
<div class='proof comment'>
**割线**
割线上的点已经被舍弃，$f(x < 0)$ **<orange>无定义**。
</div>
---
### 其它常见多值函数
\item<+-> 多值函数无处不在，除了根式函数，还有
\item<+-> 一般幂次函数 $z^\alpha$，$\alpha \in \mathbb{C}$
\item<+-> 对数函数：$\ln (z - a)$
\item<+-> 反三角函数 $\arcsin z$, $\arccos$, $\arctan$ 等
\item<+-> 单值函数、多值函数的各种复合，如
$$
\left(\frac{z - a}{z - b}\right)^\alpha, \qquad
\ln \left(z^\alpha + (1 - z^2)^\beta\right) \ , \qquad \cdots \ .
$$
---
### 其他空间上的复分析
\item<+-> 除了割线，还有别的方法治理多值性问题：多叶黎曼面 (Riemann surface)
\item<+-> 以 $f(z) = \sqrt{z}$ 为例：原点是一个 **<orange>支点**，绕原点一圈会有多值现象
$$
f(e^{2\pi i}z) = - f(z) \ .
$$
\item<+-> 杜绝多值性的老方法：画割线，禁止穿越
\item<+-> 杜绝多值性的新方法：**<orange>扩展定义域**，把 $e^{2\pi i}z$ 与 $z$ 看成两个 **<orange>不同的点**，但同时尊重 **<orange>连续性**
---
\item<+-> 先只关注单位圆，把单位圆所有点复制一遍
\begin{figure}
\centering
\tikzset{every picture/.style={line width=0.75pt}} 
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.6]
\draw  [dash pattern={on 4.5pt off 4.5pt}] (56,145.88) .. controls (56,112.81) and (82.81,86) .. (115.88,86) .. controls (148.95,86) and (175.76,112.81) .. (175.76,145.88) .. controls (175.76,178.95) and (148.95,205.76) .. (115.88,205.76) .. controls (82.81,205.76) and (56,178.95) .. (56,145.88) -- cycle ;
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (111.88,145.88) .. controls (111.88,143.67) and (113.67,141.88) .. (115.88,141.88) .. controls (118.09,141.88) and (119.88,143.67) .. (119.88,145.88) .. controls (119.88,148.09) and (118.09,149.88) .. (115.88,149.88) .. controls (113.67,149.88) and (111.88,148.09) .. (111.88,145.88) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (171.76,145.88) .. controls (171.76,143.67) and (173.55,141.88) .. (175.76,141.88) .. controls (177.97,141.88) and (179.76,143.67) .. (179.76,145.88) .. controls (179.76,148.09) and (177.97,149.88) .. (175.76,149.88) .. controls (173.55,149.88) and (171.76,148.09) .. (171.76,145.88) -- cycle ;
\draw  [draw opacity=0] (159.28,115.28) .. controls (161.73,118.76) and (163.8,122.59) .. (165.4,126.74) .. controls (167.7,132.69) and (168.86,138.8) .. (168.98,144.83) -- (115.88,145.88) -- cycle ; \draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ] (159.28,115.28) .. controls (161.73,118.76) and (163.8,122.59) .. (165.4,126.74) .. controls (167.7,132.69) and (168.86,138.8) .. (168.98,144.83) ;  
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (159.84,123.42) -- (159.23,114.5) -- (166.74,119.36) -- (161.26,117.95) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=0.07 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=0.5 ] (167.09,170.21) .. controls (167.09,168) and (168.88,166.21) .. (171.09,166.21) .. controls (173.3,166.21) and (175.09,168) .. (175.09,170.21) .. controls (175.09,172.42) and (173.3,174.21) .. (171.09,174.21) .. controls (168.88,174.21) and (167.09,172.42) .. (167.09,170.21) -- cycle ;
\draw  [draw opacity=0] (168.61,152.15) .. controls (167.69,159.88) and (165.07,167.3) .. (161,173.87) -- (115.88,145.88) -- cycle ; \draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=0.5 ] (168.61,152.15) .. controls (167.69,159.88) and (165.07,167.3) .. (161,173.87) ;  
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=0.09 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=0.5 ] (163.53,156.08) -- (169.23,149.18) -- (171.32,157.88) -- (168.33,153.08) -- cycle ;
\draw   (326.95,126.67) .. controls (326.95,115.62) and (357.47,106.67) .. (395.12,106.67) .. controls (432.77,106.67) and (463.29,115.62) .. (463.29,126.67) .. controls (463.29,137.71) and (432.77,146.67) .. (395.12,146.67) .. controls (357.47,146.67) and (326.95,137.71) .. (326.95,126.67) -- cycle ;
\draw   (326.95,176.67) .. controls (326.95,165.62) and (357.47,156.67) .. (395.12,156.67) .. controls (432.77,156.67) and (463.29,165.62) .. (463.29,176.67) .. controls (463.29,187.71) and (432.77,196.67) .. (395.12,196.67) .. controls (357.47,196.67) and (326.95,187.71) .. (326.95,176.67) -- cycle ;
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (391.12,126.67) .. controls (391.12,124.46) and (392.91,122.67) .. (395.12,122.67) .. controls (397.33,122.67) and (399.12,124.46) .. (399.12,126.67) .. controls (399.12,128.88) and (397.33,130.67) .. (395.12,130.67) .. controls (392.91,130.67) and (391.12,128.88) .. (391.12,126.67) -- cycle ;
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (391.12,176.67) .. controls (391.12,174.46) and (392.91,172.67) .. (395.12,172.67) .. controls (397.33,172.67) and (399.12,174.46) .. (399.12,176.67) .. controls (399.12,178.88) and (397.33,180.67) .. (395.12,180.67) .. controls (392.91,180.67) and (391.12,178.88) .. (391.12,176.67) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (459.29,126.67) .. controls (459.29,124.46) and (461.08,122.67) .. (463.29,122.67) .. controls (465.5,122.67) and (467.29,124.46) .. (467.29,126.67) .. controls (467.29,128.88) and (465.5,130.67) .. (463.29,130.67) .. controls (461.08,130.67) and (459.29,128.88) .. (459.29,126.67) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (459.29,176.67) .. controls (459.29,174.46) and (461.08,172.67) .. (463.29,172.67) .. controls (465.5,172.67) and (467.29,174.46) .. (467.29,176.67) .. controls (467.29,178.88) and (465.5,180.67) .. (463.29,180.67) .. controls (461.08,180.67) and (459.29,178.88) .. (459.29,176.67) -- cycle ;
\draw (184.76,139.28) node [anchor=north west][inner sep=0.75pt]  [color={rgb, 255:red, 139; green, 87; blue, 42 }  ,opacity=1 ]  {$选择f( 1) =1$};
\draw (173.09,173.61) node [anchor=north west][inner sep=0.75pt]  [color={rgb, 255:red, 139; green, 87; blue, 42 }  ,opacity=1 ]  {$f\left( e^{2\pi i} 1\right) =-1$};
\draw (115.88,153.28) node [anchor=north] [inner sep=0.75pt]    {$f( 0) =0$};
\draw (460,130.07) node [anchor=north west][inner sep=0.75pt]    {$f\left( e^{2\pi i} 1\right) =-1$};
\draw (465.29,180.07) node [anchor=north west][inner sep=0.75pt]    {$f( 1) =1$};
\end{tikzpicture}
<center>
![width=0.8\textwidth](image/volcano.png)
</center>
</center>
---
\item<+-> 两个原点合同，因为 $f(z)$ 在原点没有多值性
<div class='proof comment'>
**极简主义**
数学家崇尚 **<orange>极简主义**，不引入非必要的东西。$f(z)$ 在原点处本身 $f(0) = 0$，**<red>没有多值** 现象，**<red>不需要** 复制多一份原点。
</div>
---
\item<+-> 为了保持连续性，把两个单位圆连接起来
<center>
![width=0.7\textwidth](image/connect.jpg)
</center>
---
<center>
\href{./animation/DoubleCircle.html}{![width=0.8\textwidth](DoubleCircle.png)}
</center>
---
\item<+-> 为了保持连续性，把两个单位圆连接起来
\begin{figure}
\centering
\tikzset{every picture/.style={line width=0.75pt}} 
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1]
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (176.12,94.67) .. controls (176.12,92.46) and (177.91,90.67) .. (180.12,90.67) .. controls (182.33,90.67) and (184.12,92.46) .. (184.12,94.67) .. controls (184.12,96.88) and (182.33,98.67) .. (180.12,98.67) .. controls (177.91,98.67) and (176.12,96.88) .. (176.12,94.67) -- cycle ;
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (176.12,144.67) .. controls (176.12,142.46) and (177.91,140.67) .. (180.12,140.67) .. controls (182.33,140.67) and (184.12,142.46) .. (184.12,144.67) .. controls (184.12,146.88) and (182.33,148.67) .. (180.12,148.67) .. controls (177.91,148.67) and (176.12,146.88) .. (176.12,144.67) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (244.29,94.67) .. controls (244.29,92.46) and (246.08,90.67) .. (248.29,90.67) .. controls (250.5,90.67) and (252.29,92.46) .. (252.29,94.67) .. controls (252.29,96.88) and (250.5,98.67) .. (248.29,98.67) .. controls (246.08,98.67) and (244.29,96.88) .. (244.29,94.67) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (244.29,144.67) .. controls (244.29,142.46) and (246.08,140.67) .. (248.29,140.67) .. controls (250.5,140.67) and (252.29,142.46) .. (252.29,144.67) .. controls (252.29,146.88) and (250.5,148.67) .. (248.29,148.67) .. controls (246.08,148.67) and (244.29,146.88) .. (244.29,144.67) -- cycle ;
\draw    (180.12,94.67) -- (180.12,144.67) ;
\draw    (180.12,144.67) -- (180.12,169.87) ;
\draw    (180.12,64.27) -- (180.12,94.67) ;
\draw [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ]   (248.29,144.67) .. controls (250.13,118.52) and (112.13,119.32) .. (111.95,144.67) .. controls (111.76,170.01) and (221.45,168.98) .. (235.16,153.84) .. controls (248.88,138.7) and (248.72,120.13) .. (248.29,94.67) .. controls (247.85,69.21) and (112.35,69.46) .. (111.95,94.67) .. controls (111.54,119.88) and (221.21,116.54) .. (234.88,106.7) .. controls (248.55,96.85) and (238.63,149.05) .. (240.33,153.83) .. controls (242.04,158.61) and (244.33,155.12) .. (246.48,149.83) ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (200.29,130.02) -- (192.63,125.41) -- (200.91,122.04) -- (196.61,125.72) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (243.83,119.94) -- (248.06,112.06) -- (251.83,120.17) -- (247.95,116.06) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (210.96,81.02) -- (203.29,76.41) -- (211.58,73.04) -- (207.28,76.72) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (162.52,109.5) -- (170.27,113.96) -- (162.05,117.48) -- (166.28,113.72) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (158.85,160.16) -- (166.61,164.62) -- (158.39,168.15) -- (162.61,164.39) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (245.97,121.83) -- (241.38,129.51) -- (237.99,121.24) -- (241.68,125.52) -- cycle ;
\draw (250.29,98.07) node [anchor=north west][inner sep=0.75pt]    {$f\left( e^{2\pi i} 1\right) =-1$};
\draw (250.29,148.07) node [anchor=north west][inner sep=0.75pt]    {$f( 1) =1$};
\draw (157.63,186.4) node [anchor=north west][inner sep=0.75pt]   [align=left] {第二步};
\end{tikzpicture}
![width=0.4\textwidth](image/two-sheeted.png)
</center>
<div class='proof comment'>
**一阶支点**
注意原点是一阶支点，**<orange>转两圈**，多值性消失。
</div>
---
\item<+-> $\sqrt{z}$ 对应的 Riemann surface
\begin{figure}
\centering
![width=0.4\textwidth](image/sqrt.svg.png)
双叶
</center>
---
\item<+-> $\sqrt{z}$ 对应的 Riemann surface
<div class='proof comment'>
**虚假的交汇**
双叶黎曼面实际 **<red>并没自相交**，图中的交线来源于三维绘图空间的束缚。可以在高维空间中绘制不自交的双叶黎曼面。
</div>
---
\item<+-> $z^{1/3}$ 对应的 Riemann surface
\begin{figure}
\centering
![width=0.4\textwidth](image/cube_root.svg.png)
三叶
</center>
---
\item<+-> $z^{1/4}$ 对应的 Riemann surface
\begin{figure}
\centering
![width=0.4\textwidth](image/4th_root.svg.png)
四叶
</center>
---
\item<+-> $\ln z$ 对应的 Riemann surface
\begin{figure}
\centering
![width=0.3\textwidth](image/log.svg.png)
无限城
</center>
---
### 其他空间上的复分析
\item<+-> 除了 $\mathbb{C}$，还可以在其他空间上研究复变函数和微积分
\item<+-> 最简单的例子：$\mathbb{C} \cup \{\infty\} = \mathbb{C}P^1$ 上的复变函数实际上就是我们课程的内容
\begin{figure}
\tikzset{every picture/.style={line width=0.75pt}} 
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.7]
\draw  [fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.12 ][line width=1.5]  (130,116.36) .. controls (130,65.35) and (171.35,24) .. (222.36,24) .. controls (273.38,24) and (314.73,65.35) .. (314.73,116.36) .. controls (314.73,167.38) and (273.38,208.73) .. (222.36,208.73) .. controls (171.35,208.73) and (130,167.38) .. (130,116.36) -- cycle ;
\draw  [draw opacity=0][dash pattern={on 4.5pt off 4.5pt}] (130,116.36) .. controls (130,99.8) and (171.24,86.36) .. (222.11,86.36) .. controls (272.99,86.36) and (314.23,99.8) .. (314.23,116.36) .. controls (314.23,116.36) and (314.23,116.36) .. (314.23,116.36) -- (222.11,116.36) -- cycle ; \draw  [dash pattern={on 4.5pt off 4.5pt}] (130,116.36) .. controls (130,99.8) and (171.24,86.36) .. (222.11,86.36) .. controls (272.99,86.36) and (314.23,99.8) .. (314.23,116.36) .. controls (314.23,116.36) and (314.23,116.36) .. (314.23,116.36) ;  
\draw  [draw opacity=0] (314.73,116.36) .. controls (314.73,132.93) and (273.49,146.36) .. (222.61,146.36) .. controls (171.74,146.36) and (130.5,132.93) .. (130.5,116.36) .. controls (130.5,116.36) and (130.5,116.36) .. (130.5,116.36) -- (222.61,116.36) -- cycle ; \draw   (314.73,116.36) .. controls (314.73,132.93) and (273.49,146.36) .. (222.61,146.36) .. controls (171.74,146.36) and (130.5,132.93) .. (130.5,116.36) .. controls (130.5,116.36) and (130.5,116.36) .. (130.5,116.36) ;  
\end{tikzpicture}
</center>