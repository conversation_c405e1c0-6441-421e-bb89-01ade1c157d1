---
marp: true
theme: rose-pine-dawn
paginate: true
_paginate: skip
size: 16:9

math: mathjax

---
$$
\newcommand\blue[1]{{\color[rgb]{0.20, 0.43, 0.75}{#1}}}
\newcommand\red[1]{{\color[rgb]{0.839844, 0.507813, 0.488281}{#1}}}
\newcommand\green[1]{{\color[rgb]{.359375, .59765625, .41015625}{#1}}}
\newcommand\gray[1]{{\color[rgb]{0.5, 0.5, 0.5}{#1}}}
\newcommand\purple[1]{{\color[rgb]{0.63515625, 0.49609375, 0.80859375}{#1}}}
\newcommand\white[1]{{\color{white}{#1}}}
\newcommand\orange[1]{{\color[rgb]{0.63515625, 0.51015625, 0.37734375}{#1}}}
$$

---

# 数学物理方法

---

### 课程概况

* 这是一门 **<orange>数学课**：核心是数学，但是处处渗透物理思想
* 同时也是一门 **<orange>语言课**：学会用复数说话，用方程描述物理，用特殊函数刻画物理演化

---

### 课程概况

* 数学是一门独立的、有内在生命力、独特价值评价体系的学科

<div class='proof comment'>

**数学不只是工具**

数学为物理提供工具，但数学 **<red>不仅仅** 是物理的工具。
</div>

* 特别地，数学是物理的 **<orange>语言**：没有数学，物理学家难以进行精确表达思想和传递信息

<div class='proof comment'>

**普世语言**

甚至可能是整个多重宇宙中所有智慧生物的普世语言。
</div>

---

### 课程概况

* 反过来，物理也可以为数学反哺许多新奇的思想和生成数学结构

<div class='proof comment'>

**来自物理的反作用**

广义相对论与微分几何，散射振幅与代数几何，杨-米尔斯理论与微分拓扑，超对称与算子代数、代数簇
</div>

---

### 课程概况

* 《数学物理方法》是众多后续课程和科研的基石
* 电动力学、量子力学：数理方程的求解

统计力学：复变函数、级数、积分技巧

量子场论：复变函数、积分技巧、数理方程的求解

广义相对论：数理方程的求解

通信与信息技术：傅里叶级数、傅里叶变换，$\delta$ 函数
* 现代理论物理可能需要远超本课程所涵盖的内容：微分与代数几何、抽象代数结构、复杂微分方程

---

### 课程概况

* 数学物理方法：主要介绍两个基本要素
  * 复数与复变函数
  * 数理方程与特殊函数 (作为数理方程的解)

---

### 本章概要

- 数的历史

- 复数

$\gray{复数、复数的表示方法、复数的性质}$
- 点集基础

$\gray{内点、边界点、聚点、特殊点集}$

- 解析函数

$\gray{复可导性、Cauchy-Riemann 条件、多值函数与支点}$

---

# 一

## 数的历史

---

### 自然数

* 自然数 (natural numbers)，$\mathbb{N}$
  $$
  \begin{align}
  \orange{0}, 1, 2, 3, 4, \ldots
  \end{align}
  $$
* 用于标记现实物体、事件的数量

---

### 自然数

* 原始社会用 **<orange>绳结** 计数

<center>

![width:400px](mdc:image/knots.jpg)

印加文明的记事绳结：奇普 (khipu)
</center>
# Slide title

---

### 无理数

* 无理数 (irrational numbers)
  $$
  \begin{align}
  e, \qquad \pi, \qquad \sqrt{2}, \qquad \sqrt{3}, \qquad \ldots
  \end{align}
  $$

---

### 无理数

* 毕达哥拉斯学派的数学家 Hippasus 提出 $\sqrt{2}$ 是一种之前没有研究过的数：**<orange>无理数**。毕达哥拉斯认为「数是绝对的、万物皆数 (all is number)」，拒绝接受无理数的存在，对 Hippasus 处以 **<red>死刑** (推海里淹死)。

<center>

![width:320px](mdc:image/Hippasus.png)
</center>

* 公元前一世纪左右，《九章算术》记载了开方运算中出现的无理数

---

### 无理数

* 在 16 世纪的欧洲，人们逐渐接受 **<orange>负数**、**<orange>分数**，到 18、19 世纪人们以 **<green>代数数** 和 **<green>超越数** 两种角度思考也研究无理数。

<div class='proof comment'>

**代数数**

即整系数多项式的根。
</div>

18, 19世纪数学家 Lambert、Legendre、Weierstrass、Cantor、Dedekind 等都对无理数进行深入的研究

---

### 实数

* 实数 (real numbers), $\mathbb{R}$：有理数与无理数的并集
* 中世纪时，阿拉伯数学家提出实数的概念
* 「实 (real)」来自 17 世纪的笛卡尔

<div class='proof comment'>

**实根与虚根**

17 世纪的数学家已经在研究代数方程根，分为实根 (real roots) 和虚根 (imaginary roots)
</div>

---

### 实数

* 有理数和无理数密密麻麻 **<orange>无缝** 形成实数轴 (real line)

<center>

![width:560px](mdc:image/real.png)
</center>

<div class='proof comment'>

**拓扑**

"密密麻麻、无缝"是一种拓扑概念
</div>

---

# 二

## 复数

---

### 实数

* 实数及其加、乘运算满足基本性质：对任意 $a, b \in \mathbb{R}$
  * 加法 **<orange>交换** 律 $a + b = b + a$，**<orange>结合** 律 $(a + b) + c = a + (b + c)$
  * 乘法 **<orange>交换** 律 $a \cdot b = b a$，**<orange>结合** 律 $(a \cdot b) \cdot c = a \cdot (b \cdot c)$
  * 加乘 **<orange>分配** 律：$a \cdot (b + c) = a b + a c$
  * 有加法 **<green>单位元** $0 \in \mathbb{R}$：$0 + a = 0 + a = a$
  * 有乘法 **<green>单位元** $1 \in \mathbb{R}$：$1 \cdot a = a \cdot 1 = a$
  * 有乘法 **<orange>零** 元 $0$：$a \cdot 0 = 0 \cdot a = 0$
  * 存在加法 **<green>逆元** $- a$：$(-a) + a = 0 $
  * 当 $a \ne 0$，存在乘法 **<green>逆元** $a^{-1}$：$a^{-1} \cdot a = 1 $

---

### 复数

* 问题一：是否有别的非实数的东西满足上面这些运算性质？
* 问题二：非常简单的实 (整) 系数代数方程 $x^2 + 1 = 0$ 是否有解？
* 目标：扩充实数，使得
  * A: 维持实数的代数性质
  * B: 使得实方程 $x^2 + 1 = 0$ 有解

---

### 复数

* 定义 **<green>虚数单位 (imaginary unit) $i$**，实现目标 B


<div class='proof comment'>

**虚数单位**

有的文献和编程语言使用 $j$ 或者 $I$ 代替 $i$，
</div>

---

### 复数

* 定义 **<green>复数 (complex numbers)** 为所有形如 $a + bi$ 的物体，其中 $a, b \in \mathbb{R}$
* 对任意复数 $z = a + b i$，定义 **<green>实部 $\operatorname{Re}z \coloneqq a$**，**<green>虚部 $\operatorname{Im}z \coloneqq b$**
* 全体复数形成的集合称为 **<green>复数集 $\mathbb{C}$**
# Slide title

---

### 复数的四则运算：例子

* 考虑一个虚部为零的复数 (其实就是实数) $z_a \coloneqq a + 0 i$，一个纯虚数 $z_b \coloneqq 0 + bi$，则它们的和
  $$
  \begin{align}
  z_a + z_b = (a + 0) + (0 + b)i = a + b i \in \mathbb{C}\ .
  \end{align}
  $$

<div class='proof comment'>

**复数的分解**

任何一个复数都可以分解为一个 **<orange>实数** 和一个 **<orange>纯虚数** 的和。
</div>

---

### 复数的代数运算

* 理解这些四则运算有两种思路。

* (1) 把这些 **<orange>具体的式子** 作为加减乘除运算的 **<orange>定义**，可以验证它们满足之前列举的所有性质 (交换律、结合律、分配律等)

* (2) 把运算应满足的 **<orange>规律** (交换律、结合律等) 作为出发点、**<orange>定义**，则上面这些具体的运算规则可以被推导出来，从而获得复数及其运算。

---

### 复数的代数运算

<div class='proof comment'>

**定义对象的新思路**

"It is **<red>not** who I am underneath, but **<orange>what I do**，that defines me."

——著名慈善家、企业家 Bruce Wayne
</div>

<center>

![width:720px](mdc:image/Bruce.jpg)
</center>

---

### 复数的代数运算

* 复数乘法定义实际上就是 **<orange>分配律**、**<orange>交换律**、**<orange>结合律** 的结果：把 $a_{1,2} + b_{1,2} i$ 均看成是 **<orange>实数** **<orange>加** **<orange>纯虚数**，
  $$
  \begin{align}
  (a_1 + b_1 i) (a_2 + b_2 i)
  = & \ 
  a_1 a_2 + b_1 i b_2 i + a_1 b_2 i + b_1 i a_2 \\
  = & \ a_1 a_2 + b_1 b_2 \orange{i^2} + (a_1 b_2 + b_1 a_2) i \\
  = & \ a_1 a_2 \orange{-} b_1 b_2 + (a_1 b_2 + b_1 a_2) i
  \end{align}
  $$

---

### 复数的代数运算

* $z$ 的模方 (modular-square)
  $$
  \begin{align}
  z \bar z = z z^* = (x + i y)(x - i y) = x^2 + y^2 \ .
  \end{align}
  $$

<div class='proof comment'>

**因式分解**

$x^2 + y^2$ 可以在复数范围内作因式分解：原本不可能的事情，在复数范围内变得可能。
</div>

* **<green>幂次运算**：对于 $n = 0, 1,2, 3, \dots$，
  $$
  \begin{equation}
  z^n = z \cdot z \cdot ... \cdot z \ .
  \end{equation}
  $$
* 常用结果：$i^{2k} = (-1)^k$，$i^{4k} = 1$, $k \in \mathbb{N}$

---

### 复数的表示

* **<orange>代数式表示/定义**：对任意 $z \in \mathbb{C}$，存在 $x, y \in \mathbb{R}$ 使得
  $$
  \begin{align}
  z = x + y i \ .
  \end{align}
  $$
* **<orange>矢量表示**：对任意 $z = x + yi \in \mathbb{C}$，可以在 $\mathbb{R}^2$ 上找到一个点/矢量 $(x,y)$ 对应

允许矢量平移，矢量加减法 $=$ 复数加减法

<center>

![width:320px](mdc:image/vector-rep.jpg)
</center>

---

### 复数的表示

* **<orange>三角表示法**：用极坐标来标记矢量表示法中的 $x,y$，
  $$
  \begin{align}
  x = r \cos \theta, \quad y = r \sin \theta \qquad \Rightarrow
  \qquad
  z = & \ r \cos \theta + i r \sin \theta\\
  = & \ r(\cos\theta + i \sin \theta)\ .
  \end{align}
  $$
  角度 $\theta \in \mathbb{R}$ 称为 **<green>辐角 $\operatorname{arg} z$**，长度 $r$ 称为 **<green>模 (modulus) $|z|$**, 也称为绝对值。
  $$
  \begin{align}
  r = |z| = \sqrt{x^2 + y^2} \ .
  \end{align}
  $$

<center>

![width:240px](mdc:Trig-Rep.png)
</center>

---

### 复数的表示

* **<orange>指数表示法**：**<green>$e^{i\theta} \coloneqq \cos \theta + i \sin \theta$**, 从而
  $$
  \begin{align}
  z = r e^{i \theta} = r \exp \left[i \theta\right] \ .
  \end{align}
  $$

<center>

![width:400px](mdc:Exponential_Rep.png)
</center>

---

### 复数的表示

<div class='proof comment'>

**纯虚数的指数的理解**

注意 $\theta$ 是 **<orange>实数**，$i \theta$ 是 **<orange>纯虚数**。$i\theta$ 的指数应该通过 Taylor 展开，
</div>

---

### 复数的表示：辐角多值性

* 三角表示和指数表示中都涉及 **<orange>辐角**。
* 给定一个非零 $z$，其辐角 **<red>不唯一**。对任意 **<green>整数 $k$**，都有
  $$
  \begin{align}
  z = r (\cos \theta + i \sin \theta)
  = r (\cos (\theta + 2\pi k) + i \sin (\theta + 2\pi k)) \ .
  \end{align}
  $$
  因此，指数表达式也不唯一，
  $$
  \begin{align}
  z = re^{i \theta} = r e^{i (\theta + 2\pi k)} \ .
  \end{align}
  $$
* 有时候，人们会 **<orange>人为限制** $\theta$ 的一个取值范围，如 $\theta \in [0, 2\pi)$
# Slide title

---

### 指数表示下的乘除运算

<div class="proof">

**证明**

利用三角函数的积化和差，得到
</div>

---

### 指数表示下的乘除运算

<div class="proof">

**证明**

对于除法，
</div>

---

### 总结

* 三种常用表示：代数表示、三角表示、指数表示
  $$
  \begin{align}
  z = x + i y = r \cos\theta + i r \sin \theta = r e^{i \theta} \ .
  \end{align}
  $$
* 模长 $|z|$
  $$
  \begin{align}
  |z| = r = \sqrt{(r \cos\theta)^2 + (r \sin \theta)^2} = \sqrt{x^2 + y^2}
  = \sqrt{ z \bar z}
  \end{align}
  $$
  模方 (modulus squared)
  $$
  \begin{align}
  |z|^2 = z \bar z \ .
  \end{align}
  $$

---

### 总结

* $| - z| = |z|$
* $|e^{i \theta}| = 1$，其中 $\theta \in \mathbb{R}$
* $|z_1 z_2| = |z_1| |z_2|$

---

### 代数基本定理

* **<orange>代数基本定理**：任何一个 $n$-次复系数多项式都有 $n$ 个复数根

<div class='proof comment'>

**重根**

这 $n$ 个复数根可能有 **<orange>重复**。
</div>

<div class='proof comment'>

**因式分解**

$n$-次复系数多项式 $a_n x^n + a_{n - 1}z^{n - 1} + ... + a_0$ 一定可以分解为
其中 $z_i$ 为根。
</div>

---

### 代数基本定理

* **<orange>韦达定理**：对任意复多项式 $P(z) = a_n z^n + a_{n - 1}z^{n - 1} + \cdots + a_0$ 的根 $z_1, \cdots, z_n$ 满足
  $$
  \begin{equation}
  (-1)^n a_n \prod_{k = 1}^{n}z_k = a_0, \qquad 
  a_n\sum_{k = 1}^{n}z_k = - a_{n - 1} \ .
  \end{equation}
  $$

---

### 无穷远点

* $\mathbb{R}$ 与无穷远点

<center>

![width:480px](animation/ExtendedR1.png)
</center>

* $\mathbb{R} \cup \infty$ 实际上就是一维圆圈 $S^1$。

---

### 无穷远点

* $\mathbb{C}$ 与无穷远点 $\infty$ 合并为 **<green>扩充的复平面**
* $\mathbb{C} \cup \{\infty\}$ 实际上就是二维球面 $S^2$。

---

### 无穷远点

<center>

![width:600px](image/extended-C.svg)
</center>

---

# 三

## 点集基础
# Slide title

---

### 点与点集的相对关系：内点

<div class='proof comment'>

**内点例子**

若 $z \in N(z_0, r)$，则 $z$ 是 $N(z_0, r)$ 的内点。
</div>

<div class='proof comment'>

**内点判断**

考虑点集 $S \coloneqq \{z \in \mathbb{C} \ | \ |z| \le 1\}$。考虑

(1) $z_0 = 0.9999 \ldots 9$ (有限个 9)

(2) $z_0 = 0.9999 \ldots $ (无限个 9)
</div>

<div class='proof comment'>

**内点判断**

考虑点集 $S \coloneqq \{z \in \mathbb{C} \ | \ 0 < |z| < 1\}$。考虑

(1) $z_0 = 0$ \ \ \ (2) $z_0 = 1$ \ \ \ (3) $z_0 = e^{\pi i /3}$
</div>

---

### 点与点集的相对关系：边界点

* **<green>定义**：考虑点集 $S \subset \mathbb{C}$，$z_0 \in \mathbb{C}$。若对 $\forall \epsilon > 0$，均有
  $$
  \begin{align}
  N(z_0, \epsilon) \cap S \ne \emptyset \ (\text{有交}) \ , \qquad
  N(z_0, \epsilon) \not\subset S \ (\text{不被包含}) \ ,
  \end{align}
  $$
  则称 $z_0$ 为 $S$ 的 **<green>边界点**。

<div class='proof comment'>

**形象理解**

点集 $S$ 的边界点 $z_0 $ 与点集 $S$ **<orange>若即若离**

<center>

![width:240px](mdc:image/touch.jpg)
</center>
</div>

---

### 点与点集的相对关系：边界点

* **<green>定义**：考虑点集 $S \subset \mathbb{C}$，$z_0 \in \mathbb{C}$。若对 $\forall \epsilon > 0$，均有
  $$
  \begin{align}
  \text{相交非空}~N(z_0, \epsilon) \cap S \ne \emptyset \ , \quad
  \text{不完全包含}~N(z_0, \epsilon) \not\subset S \ ,
  \end{align}
  $$
  则称 $z_0$ 为 $S$ 的 **<green>边界点**。

<div class='proof comment'>

**边界点的从属关系**

若 $z_0$ 是 $S$ 的边界点，则 $z_0$ 可能 **<orange>属于** 也可能 **<orange>不属于** $S$。这跟 **<orange>内点** 是 **<red>不一样** 的。
</div>

---

### 点与点集的相对关系：边界点

* 一个点集 $S$ 的全体边界点所构成的集合称为 $S$ 的 **<green>边界**，记作 **<green>$\partial S$**。

---

### 点与点集的相对关系：边界点

<div class='proof comment'>

**边界点例子**

考虑点集 $S \coloneqq \{z \in \mathbb{C} \ | \ 0 < |z| < 1\}$。则原点 $z = 0$ 是 $S$ 的边界点。

<center>

![width:500px](mdc:image/boundary-point-example.svg)
</center>
</div>

---

### 点与点集的相对关系：边界点

<div class='proof comment'>

**曲线的边界**

考虑一条连续的曲线 $C \subset \mathbb{C}$，则其边界是其自身 $\partial C = C$。

<center>

![width:500px](mdc:image/boundary-point-example-2.svg)
</center>
</div>

---

### 点与点集的相对关系：边界点

<div class='proof comment'>

**孤立点的边界**

任何一个点 $z$ 可以构成一个点集 $S = \{z\}$。其边界为 $\partial S = \{z\}$。

<center>

![width:500px](mdc:image/isolated-point.svg)
</center>
</div>

---

### 点与点集的相对关系：例子

* 设 $S \coloneqq \{|z| \le 1\}$。

(1) 则 $z = 1$ 是 **<orange>边界点**

(2) 则 $z = 0$ 是 **<orange>内点**
* 设 $S \coloneqq \{|z| \le 1\}$。则 $\partial S = $
  $$
  \begin{equation}
  \{|z| = 1\}, \qquad \text{or} \qquad
  \{e^{i \theta} \ | \ \theta \in \mathbb{R}\} \ .
  \end{equation}
  $$
* 设 $S \coloneqq \{|z| < 1\}$。则 $\partial S = $
  $$
  \begin{equation}
  \{|z| = 1\}, \qquad \text{or} \qquad
  \{e^{i \theta} \ | \ \theta \in \mathbb{R}\} \ .
  \end{equation}
  $$

---

### 点与点集的相对关系：聚点

* **<green>定义**：设 $S \subset \mathbb{C}$，$z_0 \in \mathbb{C}$。若对 $\forall \epsilon > 0$ 始终有
  $$
  \begin{align}
  \text{相交非空} ~ & N(z_0, \epsilon) \cap S \ne \emptyset \\
  & N(z_0, \epsilon) \cap S \ \text{包含 「$z_0$ 以外的点」} \ ，
  \end{align}
  $$
  则称 $z_0$ 为 $S$ 的一个 **<green>聚点**。

<div class='proof comment'>

**从属关系**

聚点 $z_0$ 可能 **<orange>属于** 也可能 **<orange>不属于** $S$。
</div>

---

### 点与点集的相对关系：聚点

<div class='proof comment'>

**无限逼近**

说明 $z_0$ 能够被 **<orange>$S$ 的点** **<orange>无限逼近**。

<center>

![width:560px](mdc:image/聚点-2.png)
</center>
</div>
# Slide title

---

### 特殊点集

* **<green>定义**：设 $S\subset \mathbb{C}$，若 $\forall z \in S$ **<orange>均** 为 $S$ 的 **<orange>内点**，则称 $S$ 为 **<green>开集**

<div class='proof comment'>

**开集的意义**

开集用于刻画、标记 **<orange>邻近关系**。

<center>

![width:500px](mdc:image/significance-of-open-set.svg)
</center>
</div>

---

### 特殊点集

* 邻域是特殊的开集
* 开集的具体定义依赖 **<orange>邻域** 本身：倘若邻域使用 **<orange>别的形状** 定义，则开集的具体概念也会跟着改变
* 开集公理 $\Rightarrow$ **<green>拓扑结构**

(1) 有限或无限个开集的 **<orange>并** 还是 **<orange>开集**

(2) 有限个开集的 **<orange>交** 还是 **<orange>开集**

(3) $\mathbb{C}$ 与 $\emptyset$ 是 **<orange>开集**

---

### 特殊点集

* 存在其他满足开集公理的"开集概念" (拓扑结构)

<div class='proof comment'>

**离散拓扑**

当前的开集概念：任何一个点 $z$ 的任意邻域中都有 **<orange>无数个邻居**——**<orange>非孤立性**。

**<orange>离散拓扑**：任何一个点 $z$ 都存在一个 **<orange>最小邻域**，该邻域只包含它自己——**<orange>孤立性**
</div>

---

### 特殊点集

* **<green>定义**：若开集 $U$ 内任何两点都可以用一条 **<orange>完全** 属于该集合的曲线连接，则称该开集 $U$ 是 **<green>连通** 的。

* 非空、**<orange>连通** 的开集称为 **<green>区域 (domain)**

<center>

![width:500px](mdc:image/domain.svg)
</center>

---

### 特殊点集

* **<green>定义**：区域 $D$ 及其边界 $\partial D$ 的并集称为一个 **<orange>闭域** 或 **<green>$D$ 的闭包**，记作 $\bar D$。

* **<green>定义**：若区域 $D$ 内任何 **<orange>简单闭曲线** 的 **<orange>内部** 均属于 $D$，则称 $D$ 是 **<green>单连通** 的，否则称为 **<green>复连通**

<center>

![width:500px](mdc:image/simply-connected.svg)
</center>

---

# 四

## 解析函数

---

### 复变函数定义

* 设 $S \subset \mathbb{C}$。若对 $\forall z \in S$，按照某个 **<orange>规则 $f$** 有 **<orange>唯一** 的复数 $w$ 与之对应，则 $f$ 定义了一个 **<green>单值复变函数**，定义域为 $S$。
* 设 $S \subset \mathbb{C}$。若对 $\forall z \in S$，按照某个 **<orange>规则 $f$** 有 **<orange>一个或多个** 的复数 $w$ 与之对应，则 $f$ 定义了一个 **<green>多值复变函数**，定义域为 $S$。
* 不管是单值还是多值复变函数，都简记为
  $$
  \begin{align}
  w = f(z) \qquad
  \text{or}
  \qquad
  f(z)\ .
  \end{align}
  $$

---

### 复变函数定义

* 记 $w = u + i v$，$z = x + i y$，则
  $$
  \begin{align}
  w = f(z) = u(z) + i v(z) = u(x, y) + i v(x, y) \ .
  \end{align}
  $$
  换言之，一个复变函数相当于 **<orange>两个** **<orange>二元实函数**，分别称为 $f$ 的实部 $\operatorname{Re} f$ 和虚部 $\operatorname{Im} f$。

---

### 极限

* **<green>定义**：设 $w = f(z)$ 的 **<orange>定义域是 $S$**，设某点 $z_0$ 为 $S$ 的 **<orange>聚点**。

若 $\exists w_0 \in \mathbb{C}$，s.t. 对 **<orange>$\forall \epsilon > 0$**，都 **<green>$\exists \delta > 0$** s.t. 对 $\forall z \in (N(z_0, \delta) \cap S) - \{z_0\}$ 都有 $|f(z) - w_0| < \epsilon $, 则称 $f$ 在 $z_0$ 处的 **<green>极限存在**，并称 $w_0$ 是 $f(z)$ 在 $z_0$ 处的 **<green>极限**，记作 $w_0 = \lim_{z \to z_0} f(z)$。

<center>

![width:280px](mdc:image/limit.png)
![width:280px](mdc:image/dio.png)
</center>

---

### 极限

<div class='proof comment'>

**翻译翻译**

<center>

![width:480px](mdc:image/translate.jpeg)
</center>

**<orange>极限存在** 就是，对于 **<orange>甲方** 给出的任意小 **<orange>精度要求 $\epsilon$**，我们总能 **<orange>在定义域 $S$ 中** 找到一个 **<orange>足够小的距离 $\delta$**，使得 **<orange>距离以内** 的所有函数值与目标值 $w_0$ 的差别 **<orange>小于** 精度要求 $\epsilon$。
</div>
# Slide title

---

### 可导性：例

* $f(z) = z^n$，$n \in \mathbb{N}$，则可以直接计算得到
  $$
  \begin{align}
  \lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z} = n z^{n - 1}\ .
  \end{align}
  $$
  因此这个 $f$ 在 $\mathbb{C}$ 上 **<orange>处处可导**。

---

### 可导性：例

* $f(z) = \bar{z}$。因此 $f(z = x + i y) = x - i y$
  $$
  \begin{align}
  \lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z} = \lim_{\substack{\Delta x \to 0 \\ \Delta y \to 0}} \frac{x + \Delta x - i (y + \Delta y) - (x - i y)}{\Delta x + i \Delta y} \ .
  \end{align}
  $$

* 若 $\Delta z$ 沿着 **<orange>实轴** 逼近 $0$，则 $\Delta y = 0$，
  $$
  \begin{align}
  \lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z}
  = \lim_{\substack{\Delta x \to 0 \\ \Delta y \to 0}} \frac{\Delta x }{\Delta x} = \orange{1} \ .
  \end{align}
  $$

* 若 $\Delta z$ 沿着 **<orange>虚轴** 逼近 $0$，则 $\Delta x = 0$，
  $$
  \begin{align}
  \lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z}
  = \lim_{\substack{\Delta x \to 0 \\ \Delta y \to 0}} \frac{ - i \Delta y }{i \Delta y} = \red{- 1} \ .
  \end{align}
  $$

---

### 可导性：例

* 极限不存在，因此 $f(z) \coloneqq \bar z$ **<red>处处不可导**。

---

### 解析函数

* **<green>定义**：倘若 $f$ 在 $z_0$ 处不解析 (包括没有定义)，但是 $f$ 在 $z_0$ 的任意邻域内 **<orange>都有** 解析点，则称 $z_0$ 为 $f$ 的 **<green>奇点 (singularity)**。

<div class='proof comment'>

**奇点的含义**

不同领域对奇点的定义和理解会有所不同。这里是复分析中比较常用的一种理解。
</div>

---

### 求导法则

* 求导法则

(1) 线性性：$[f(z) + g(z)]' = f'(z) + g'(z)$

(2) 莱布尼兹 (Leibniz) 律：$[f(z)g(z)]' = f'(z)g(z) + f(z) g'(z)$

(3) 链式法则 (chain rule)：$f(g(z))' = \frac{df}{dg} \frac{dg}{dz}$
* 常见复解析函数导数与实光滑函数的导数在形式上是一样的。

---

### Cauchy-Riemann 条件

* **<orange>复可导性** 是很 **<orange>强** 的条件，比之前学的实变可导性要强得多：要求极限与 $\Delta z \to 0$ 的 **<orange>方向无关**

<center>

![width:480px](mdc:image/mikasa.jpg)
</center>

---

### Cauchy-Riemann 条件

* 求导：$f(z = x + i y) = u(x, y) + i v(x, y)$
  $$
  \begin{align}
  f'(z)
  = \lim_{\substack{\Delta x \to 0\\\Delta y \to 0}}\bigg[ & \ \frac{u(x + \Delta x, y + \Delta y) + i v(x + \Delta x, y + \Delta y)}{\Delta x + i \Delta y}\\
  & \ \qquad \qquad\qquad \qquad \qquad - \frac{u(x, y ) + i v(x, y )}{\Delta x + i \Delta y} \bigg] \ . \nonumber
  \end{align}
  $$
* 考虑实轴和虚轴方向

---

### Cauchy-Riemann 条件

* 考虑 $\Delta z$ 从 **<orange>实轴** 趋近零：**<orange>$\Delta y = 0$**，
  $$
  \begin{align}
  f'(z) = \frac{\partial u}{\partial x} + i \frac{\partial v}{\partial x} \ .
  \end{align}
  $$
* 考虑 $\Delta z$ 从 **<orange>虚轴** 趋近零：**<orange>$\Delta x = 0$**，
  $$
  \begin{align}
  f'(z) = \frac{\partial v}{\partial y} - i \frac{\partial u}{\partial y} \ .
  \end{align}
  $$
* 二者应该相等 (方向无关)：**<green>Cauchy-Riemann 条件**
  $$
  \begin{align}
  & \ \frac{\partial u}{\partial x} + i \frac{\partial v}{\partial x}
  = \frac{\partial v}{\partial y} - i \frac{\partial u}{\partial y}\\
  \Rightarrow & \ \green{
  \frac{\partial u}{\partial x} = \frac{\partial v}{\partial y}, \qquad
  \frac{\partial u}{\partial y} = - \frac{\partial v}{\partial x}} \ .
  \end{align}
  $$

---

### Cauchy-Riemann 条件的复坐标表示

* **<orange>定理**：实可微性 + CR 条件 $\Leftrightarrow$ 复可导性
* **<orange>定理**：实连续一阶偏导 + CR 条件 $\Leftrightarrow$ 复可导性
* 通常，物理中的函数都至少具有上述 **<orange>实** 可导性质。以后统称为 **<green>足够光滑、性质良好** 的函数。

---

### Cauchy-Riemann 条件的复坐标表示

<div class='proof comment'>

**实可微性**

二元函数 $f(x,y)$ 在 $(x_0, y_0)$ 实可微指的是 

偏导数存在但不可微的例子
</div>
# Slide title

---

### 复导数

- $z$ 与 $\bar z$ **<red>不独立**：$z \to z + \Delta \Rightarrow \bar z \to \bar z + \overline{\Delta z}$。
- 但是，偏导算符呈现 **<orange>形式上独立性 (formal independence)**，
  $$
  \begin{align}
  \frac{\partial \bar z}{\partial z} = \frac{\partial z}{\partial \bar z} = 0, \qquad
  \frac{\partial z}{\partial z} = \frac{\partial \bar z}{\partial \bar z} = 1\ .
  \end{align}
  $$
* **<orange>形式上**，$\partial_z, \partial_{\bar z}$ 跟「偏导数」完全一样：接受它们作为偏导存在，$z, \bar z$ **<orange>形式上独立**
* 通常：$f$ 满足 CR 条件/解析性/全纯性 $\Leftrightarrow$ $f$ **<red>不含** $\bar z$ $\Leftrightarrow$ $\partial_{\bar z} f = 0$

<div class='proof comment'>

**奇点**

在 **<orange>奇点** 处，$f$ 只依赖 $z$ $\not\Rightarrow$ $\partial_{\bar z} f = 0$。
</div>

---

### 复导数

* 若 $f$ 解析/全纯，则其 **<orange>复共轭** $\overline{f(z)}$ 满足
  $$
  \begin{align}
  \partial_z \overline{f(z)} = 0\ .
  \end{align}
  $$
  这样的函数称为 **<green>反全纯 (anti-holomorphic) 函数**。
* 一般的复变函数既不是全纯也不是反全纯函数。可以用 $f(z, \bar z)$ 来标明/强调。

---

### 共轭调和函数

* 区域 $D$ 上解析的函数 $f = u + i v$ 的实部和虚部满足 CR 条件
  $$
  \begin{align}
  \frac{\partial u}{\partial x} = \frac{\partial v}{\partial y}, \qquad
  \frac{\partial u}{\partial y} = - \frac{\partial v}{\partial x} \ .
  \end{align}
  $$
* 对这些方程两边作 $\partial_x$ 和 $\partial_y$
  $$
  \begin{align}
  \partial_x: \frac{\partial^2 u}{\partial x^2} = & \ \frac{\partial^2 v}{\partial x\partial y}, 
  & \frac{\partial^2 u}{\partial x\partial y} = & \ - \frac{\partial^2 v}{\partial x^2} \\
  \partial_y: \frac{\partial^2 u}{\partial y\partial x} = & \ \frac{\partial^2 v}{\partial y^2}, 
  &\frac{\partial^2 u}{\partial y^2} = & \ - \frac{\partial^2 v}{\partial y\partial x} \ .
  \end{align}
  $$
* 稍作比较可得两条 **<green>调和 (harmonic) 方程**
  $$
  \begin{align}
  \frac{\partial^2 u}{\partial x^2} + \frac{\partial^2 u}{\partial y^2} = 0, \qquad
  \frac{\partial^2 v}{\partial x^2} + \frac{\partial^2 v}{\partial y^2} = 0
  \end{align}
  $$

---

### 共轭调和函数

* 解析函数的实部和虚部均为 **<green>调和函数**，而且二者由 CR 条件相互锁定，形成一对 **<green>共轭调和函数**。
* 从一个调和函数 $u$ (或 $v$) 出发，可以 **<orange>通过 CR 条件** 解出与之共轭的调和同伴 $v$ (或 $u$)，并由此获得 **<orange>解析函数**。

---

### 共轭调和函数：例子

* 设 $u(x,y) = x^2 - y^2$。若 $v(x,y)$ 是是与之共轭的调和函数，则必然有
  $$
  \begin{align}
  dv
  = & \ \frac{\partial v}{\partial x}dx + \frac{\partial v}{\partial y} dy
  = - \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy \\
  = & \ + 2y dx + 2x dy = d(2 xy) \ .
  \end{align}
  $$
* 于是 $v = 2 xy + C$，而 $C$ 是 **<green>任意实常数**。
* 从而 $f(z) = x^2 - y^2 + 2i xy + Ci = (x + i y)^2 + C i $ 是 **<orange>解析函数**：
  $$
  \begin{align}
  f = z^2 + C \orange{i} \ .
  \end{align}
  $$

---

### 共轭调和函数

* 区域 $D$ 中共轭调和函数的 **<orange>线积分** 求解法：
  $$
  \begin{align}
  & \ dv
  = \frac{\partial v}{\partial x}dx + \frac{\partial v}{\partial y} dy
  = - \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy \\
  \Rightarrow & \ v = \int_{(x_0, y_0)}^{(x,y)} dv + C
  = \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
  \end{align}
  $$

<div class='proof comment'>

**路径无关性**

上述积分只指定了初始点 $(x_0, y_0)$ 和终末点 $(x, y)$。
</div>

---

### 共轭调和函数

* 区域 $D$ 中共轭调和函数的 **<orange>线积分** 求解法：
  $$
  \begin{align}
  v = \int_{(x_0, y_0)}^{(x,y)} dv + C
  = \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
  \end{align}
  $$

<div class='proof comment'>

**路径无关性**

**<red>具体路径怎么选？**

<center>

![width:500px](mdc:image/three-paths.svg)
</center>
</div>

---

### 共轭调和函数

* 区域 $D$ 中共轭调和函数的线积分求解法：
  $$
  \begin{align}
  v = \int_{(x_0, y_0)}^{(x,y)} dv + C
  = \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
  \end{align}
  $$

<div class='proof comment'>

**路径几乎无关性**

绿色积分 $-$ 紫色积分 $=$ 闭合路径 $C$ 积分：
</div>

---

### 共轭调和函数

* 区域 $D$ 中共轭调和函数的线积分求解法：
  $$
  \begin{align}
  v = \int_{(x_0, y_0)}^{(x,y)} dv + C
  = \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
  \end{align}
  $$

<div class='proof comment'>

**路径几乎无关性**

蓝色积分 $-$ 绿色积分 $=$ 与 $(x, y), (x_0, y_0)$ 无关的常数 $C$：不要紧。

结论：随便选一条连接 $(x_0, y_0)$ 和 $(x,y)$ 的路径，只要 **<orange>完全在解析区内**。
</div>
# Slide title

---

### 共轭调和函数与静电场

<div class='proof comment'>

**平面电场**

**<red>如何形成二维电场？**

可以使用无穷长直均匀带电直线

<center>

![width:500px](mdc:image/infinitely-long-charged-line.svg)
</center>
</div>

---

### 初等函数

* **<green>初等函数**：常数、幂函数、指数函数、对数函数、三角函数、反三角函数，及其有限次加减乘除、复合所构成的函数。
* 导数公式与大一的内容一样

---

### 初等函数：整幂函数

* **<orange>整幂** 复函数是全 $\mathbb{C}$ 解析的函数，定义为
  $$
  \begin{align}
  z^{n = 0, 1,2,3, \ldots} = \underbrace{z \cdot z \cdot \ldots \cdot z}_{n\text{ 个}} \ , \qquad
  z^{n = -1, -2, \ldots} = \frac{1}{z^{n}} \ .
  \end{align}
  $$

<div class='proof comment'>

**确定性**

给定一个 $z \in \mathbb{C}$，则 $z^n$ 是唯一 **<orange>确定** (单值) 的。
</div>

* 满足整幂实函数的性质，
  $$
  \begin{equation}
  (z_1 z_2)^n = z_1^n z_2^n \ , \qquad (z_1 + z_2)^n = \sum_{k = 0}^{n}C_n^k z_1^k z_2^{n - k} \ .
  \end{equation}
  $$
* 若 $z = re^{i \theta}$，则 $z^n = r^n e^{i n \theta}$。

---

### 初等函数：多项式

* 整幂函数通过复系数可以组合成 $n$-次 多项式，
  $$
  \begin{align}
  P(z) = \sum_{k = 0}^{n} a_k z^k \ , \qquad a_k \in \mathbb{C} \ , \quad a_n \ne 0\ .
  \end{align}
  $$
* 比如 $P(z) = z^2 - 1$，$P(z) = z^3 - 3 z^2 + 4 + 3 i$.
* **<orange>代数基本定理**：任意 $n$-次多项式都有 $n$ 个复数根。
  $$
  \begin{equation}
  P(z) = a_n \prod_{k = 1}^{n} (z - z_k) \ .
  \end{equation}
  $$

---

### 初等函数：多项式

* 实数多项式 $P_\mathbb{R}(x) = \sum_{k = 0}^{n} r_k x^k $ 不一定有 $n$ 个 **<orange>实数根**。比如
  $$
  \begin{align}
  P(x) = x^2 + 1 \ .
  \end{align}
  $$

* 倘若允许 **<orange>复数根**，则 $x^2 + 1$ 恰好有两个根：

$+i$, $-i$

$x^2 + 1 = (x - i)(x + i)$
* 又如 $x^3 - 1$ 只有一个实根：$+1$

另外有两个复数根 $e^{\frac{2 \pi i}{3}}$, $e^{\frac{4 \pi i}{3}}$

$x^3 - 1 = (z - 1)(z - e^{\frac{2\pi i}{3}})(z - e^{\frac{4\pi i}{3}})$

---

### 初等函数：次方根函数

* 给定一个 $z \in \mathbb{C}$ 以及 $n \in \mathbb{N}_{\ge 1}$，若 $z_0 \in \mathbb{C}$ 满足 $z_0^n = z$，则称 $z_0$ 为 $z$ 的一个 **<green>$n$-次方根**，记作 $z^{\frac{1}{n}}$。
* **<orange>定理**：若 $z = re^{i \theta}$，则 $z_0 = r^{\frac{1}{n}}e^{i \frac{\theta}{n}}$ 是 $z$ 的一个 $n$-次方根。

---

### 初等函数：次方根函数

* **<orange>多值性定理**：给定 $z$ 之后，$n$-次方根 $z_0$ 有 **<orange>$n$ 个** 可能值。

<div class="proof">

**多值性**

设 $z_0$ 是一个 $n$-次方根，即 $z_0^n = z$。则必然有
都是 $n$-次方根，因为 $(e^{\frac{2 \pi i}{n} k })^n = e^{2\pi i k} = 1$，$k = 0, 1, \dots, n - 1$。
</div>

---

### 初等函数：次方根函数

* $n$-次方根的多值性来源于 **<orange>$z$ 的辐角** 的多值性
  $$
  \begin{align}
  & \ z = re^{i \theta} = r e^{i (\theta + 2\pi k)} \\
  \Rightarrow & \ z^{\frac{1}{n}} = r^{\frac{1}{n}} e^{i \frac{\theta}{n}},\qquad 
  \text{或者}, \qquad r^{\frac{1}{n}} e^{i (\frac{\theta}{n} + \frac{2\pi k}{n})} \ .
  \end{align}
  $$

---

### 初等函数：次方根函数

* $1$ 的 3 次方根
  $$
  \begin{align}
  & \ 1 = e^{0\pi i} = e^{2\pi i} = e^{4 \pi i}\\
  \Rightarrow & \ 
  1^{\frac{1}{3}} = 1, e^{\frac{2\pi i}{3}}, e^{\frac{4\pi i}{3}} \ .
  \end{align}
  $$
* 多项式方程 $x^3 - 1 = 0$ 的解即为 $1$ 的 3 次方根。
* $1$ 的 $n$ 次方根
  $$
  \begin{align}
  & \ 1 = e^{0\pi i} = e^{2\pi i} = \cdots = e^{2 \pi i (n - 1)}\\
  \Rightarrow & \ 
  1^{\frac{1}{n}} = 1, e^{\frac{2\pi i}{n}}, \cdots, e^{\frac{2\pi i (n - 1)}{n}} \ .
  \end{align}
  $$
# Slide title

---

### 多值函数

* 下面讨论具有一定解析性的函数的多值现象。

<div class='proof comment'>

**解析区域**

未说明解析区域的解析函数，就默认 **<orange>存在** 某个区域使得函数在上面解析。至于这个区域是什么，不需明说。
</div>

---

### 多值函数

* 根式函数 $w = \sqrt{z}$ 是最简单的 **<orange>多值函数**
  $$
  \begin{align}
  z \to w = \sqrt{z}:\qquad w^2, (-w)^2 = z \ .
  \end{align}
  $$
* 也可以理解为来源于 $z$ 的辐角的多值性
  $$
  \begin{align}
  z = r e^{i \theta} = r e^{i (\theta + 2\pi)} \quad \to \quad
  w = r^{\frac{1}{2}} e^{i \frac{\theta}{2}}, \quad  r^{\frac{1}{2}} e^{i \frac{\theta}{2}} \orange{e^{\pi i}}
  = \orange{-} r^{\frac{1}{2}} e^{i \frac{\theta}{2}} \ .	 \nonumber
  \end{align}
  $$

<div class='proof comment'>

**多值性**

只要 $z \ne 0$，则其平方根有 **<orange>两个** 不同值。当 $z = 0$，则平方根是 **<orange>唯一** 的。
</div>

---

### 支点

<div class='proof comment'>

**多值现象**

多值函数的函数值不能唯一确定，并不是说 **<red>完全无法得知函数值**，仅仅是有好几个确定的选项，不知道挑哪一个好而已。

比如，$f(z) = z^{1/2}$ 在 $z = 1$ 的函数值是 **<orange>非常明确** 的两个选项：$1, -1$；其它的数如 $250, 66666666, 2333$ 肯定 **<red>不是合理** 的 $f(1)$ 函数值。
</div>

---

### 支点

* **<green>定义**：对于 $w = \sqrt{z}$，当自变量 $z$ 绕原点周围 **<orange>任意小** 的闭曲线一圈，$w$ **<orange>都** 会发生 **<orange>多值现象** (具体体现为符号改变)。因此，称原点 $z = 0$ 为 $w = \sqrt{z}$ 的 **<green>支/分枝点 (branch point)**。

<center>

![width:560px](mdc:image/loki.png)
</center>

---

### 支点

<center>

![width:600px](mdc:image/branch-point.svg)
</center>

---

### 支点

<center>

![width:640px](mdc:SquareRoot.png)
</center>

---

### 支点

* 再考虑根式函数 $w = \sqrt{\orange{z - a}}$，$a \in \mathbb{C}$
* 记 $\orange{z - a} \coloneqq r e^{i \theta}$，则
  $$
  \begin{align}
  w = \sqrt{r}e^{i \frac{\theta}{2}}, \qquad - \sqrt{r}e^{i \frac{\theta}{2}} \ , \qquad \theta \text{ 是 $z - a$ 的辐角}\ .
  \end{align}
  $$
* 当 $z$ 绕 $a$ 任意小闭曲线一圈，$w$ 发生 **<orange>多值现象** (变号)。因此，称 $a$ 为函数 $w$ 的 **<green>支点**

* 根式函数 $w = \sqrt{z - a}$ 在支点 $z = a$ 处 **<orange>有定义**：$w(z = a) = 0$。

---

### 支点

<center>

![width:640px](mdc:SquareRootZminusA.png)
</center>

---

### 支点

<center>

![width:600px](mdc:image/first-order-branch-point.svg)
</center>
# Slide title

---

### 支点：例

* 当 $z$ 沿绕着 $a$ 无穷小的闭曲线一圈，出现多值现象：
  $$
  \begin{align}
  \theta_a \to \theta_a + 2\pi, \quad \theta_b \to \theta_b  \ \Rightarrow
  w = \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}}
  \to \orange{-} \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}} \ .\nonumber
  \end{align}
  $$

---

### 支点：例

* 当 $z$ 沿绕着 $a, b$ 任意 **<orange>大** 的闭曲线一圈 **<red>没有** 出现多值现象：
  $$
  \begin{align}
  \theta_{a,b} \to \theta_{a,b} + 2\pi, \Rightarrow
  w = \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}}
  \to \orange{(-)^2} \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}} \ .\nonumber
  \end{align}
  $$

<center>

![width:500px](non-multivaluedness.svg)
</center>

---

### 支点：例

* 总结：

(1) $b$ 是 $w = \sqrt{(z - a)(z - b)}$ 的一阶支点

(2) $a$ 是 $w = \sqrt{(z - a)(z - b)}$ 的一阶支点

(3) $\infty$ **<red>不是** 支点

---

### 支点：例

* 考虑 $w(z) = \sqrt{(z - a)(z - b)(z - c)}$，$a,b,c$ 互异

一阶支点：$\infty, a, b, c$

* 考虑 $w(z) = \sqrt{1 - \frac{1}{z}}$

$w(z) = \sqrt{1 - \frac{1}{z}} = \sqrt{\frac{z - 1}{z}}$，一阶支点：$0, 1$。$\infty$ **<red>不是** 支点

* 考虑 $w(z) = (z^3 - 1)^{\frac{1}{3}}$

$w(z) = [(z - 1)(z - e^{\frac{2\pi i }{3}})(z - e^{\frac{4\pi i }{3}})]^{\frac{1}{3}}$，二阶支点：$1$, $e^{\frac{2\pi i}{3}}$, $e^{\frac{4\pi i}{3}}$。$\infty$ **<red>不是** 支点

---

### 支点：例

* 考虑 $w = z^a (z - 1)^b$，$a, b \in \mathbb{Q}$

(1) 若 $a \not\in \mathbb{Z}$，则 $z = 0$ 是支点

(2) 若 $b \not\in \mathbb{Z}$，则 $z = 1$ 是支点

(3) 若 $a + b \not\in \mathbb{Z}$，则 $z = \infty$ 是支点

---

### 支点：例

* 考虑 $w = \ln z$
* 给定 $z$，所对应的对数 $w$ 不唯一：可以相差任意的 $2\pi i n$。
* 支点：$z = 0, \infty$
* 不管绕多少圈，都无法返回原值：**<green>超越 (transcendental) 支点**
* $\ln z$ 在 $z = 0$ **<orange>无定义**：与根式函数 $w = \sqrt{z}$ 不一样

---

### 支点

* 对于解析函数，支点可能是 **<orange>连续点**
* 通常认为支点是 **<orange>奇点**：支点处 **<orange>导数** **<red>无法定义**。

<center>

![width:480px](image/branch-point-derivative.png)
</center>

---

### 割线

* 为了 **<orange>禁止** 多值现象，可以从支点出发画一条曲线把 $\mathbb{C}$ 割破，称为 **<green>割线**，并 **<orange>明令禁止** $z$ 穿过割线。与此同时，**<orange>人为** **<orange>选** 定割线外 **<orange>一点** 的函数值作为 **<orange>基准**。

$\Rightarrow$ **<orange>唯一确定** 整个连通解析区的函数值，完全 **<orange>杜绝** 多值现象

<div class='proof comment'>

**多个支点**

当函数有 **<orange>多个支点** 的时候，需要 **<orange>每个** 支点都连上割线。若有支点无割线，则出现 **<orange>多值漏洞**
</div>

---

### 割线：例

* 考虑 $w = \sqrt{z - a}$，支点是 $a, \infty$

<center>

![width:500px](branch-cut.svg)
</center>
# Slide title

---

### 割线：例

* 更一般地，对 $x < 0$，割线的上岸和下岸的值已被确定，
  $$
  \begin{align}
  f(x + i \epsilon) = i |x|^{\frac{1}{2}}, \qquad
  f(x - i \epsilon) = - i |x|^{\frac{1}{2}} \ .
  \end{align}
  $$

<div class='proof comment'>

**割线**

割线上的点已经被舍弃，$f(x < 0)$ **<orange>无定义**。
</div>

---

### 其它常见多值函数

* 多值函数无处不在，除了根式函数，还有
  * 一般幂次函数 $z^\alpha$，$\alpha \in \mathbb{C}$
  * 对数函数：$\ln (z - a)$
  * 反三角函数 $\arcsin z$, $\arccos$, $\arctan$ 等
  * 单值函数、多值函数的各种复合，如
    $$
    \begin{equation}
    \left(\frac{z - a}{z - b}\right)^\alpha, \qquad
    \ln \left(z^\alpha + (1 - z^2)^\beta\right) \ , \qquad \cdots \ .
    \end{equation}
    $$

---

### 其他空间上的复分析

* 除了割线，还有别的方法治理多值性问题：多叶黎曼面 (Riemann surface)
* 以 $f(z) = \sqrt{z}$ 为例：原点是一个 **<orange>支点**，绕原点一圈会有多值现象
  $$
  \begin{equation}
  f(e^{2\pi i}z) = - f(z) \ .
  \end{equation}
  $$
* 杜绝多值性的老方法：画割线，禁止穿越
* 杜绝多值性的新方法：**<orange>扩展定义域**，把 $e^{2\pi i}z$ 与 $z$ 看成两个 **<orange>不同的点**，但同时尊重 **<orange>连续性**

---

### 其它空间上的复分析

* 先只关注单位圆，把单位圆所有点复制一遍

<center>

![width:480px](mdc:image/riemann-surface-construction.png)
</center>

<center>

![width:640px](mdc:image/volcano.png)
</center>

---

### 其它空间上的复分析

* 两个原点合同，因为 $f(z)$ 在原点没有多值性

<div class='proof comment'>

**极简主义**

数学家崇尚 **<orange>极简主义**，不引入非必要的东西。$f(z)$ 在原点处本身 $f(0) = 0$，**<red>没有多值** 现象，**<red>不需要** 复制多一份原点。
</div>

---

### 其它空间上的复分析

* 为了保持连续性，把两个单位圆连接起来

<center>

![width:560px](mdc:image/connect.jpg)
</center>

---

### 其它空间上的复分析

<center>

![width:640px](mdc:DoubleCircle.png)
</center>

---

### 其它空间上的复分析

* 为了保持连续性，把两个单位圆连接起来

<center>

![width:480px](mdc:image/riemann-surface-connection.png)
</center>

<center>

![width:320px](mdc:image/two-sheeted.png)
</center>

<div class='proof comment'>

**一阶支点**

注意原点是一阶支点，**<orange>转两圈**，多值性消失。
</div>

---

### 其他空间上的复分析

* $\sqrt{z}$ 对应的 Riemann surface

<center>

![width:320px](mdc:image/sqrt.svg.png)

双叶
</center>


---

### 结束

感谢观看！
