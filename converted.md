---
marp: true
theme: rose-pine-dawn
paginate: true
_paginate: skip
size: 16:9

math: mathjax

---
# 复数简介
$$
\newcommand\blue[1]{{\color[rgb]{0.20, 0.43, 0.75}{#1}}}
\newcommand\red[1]{{\color[rgb]{0.839844, 0.507813, 0.488281}{#1}}}
\newcommand\green[1]{{\color[rgb]{.359375, .59765625, .41015625}{#1}}}
\newcommand\gray[1]{{\color[rgb]{0.5, 0.5, 0.5}{#1}}}
\newcommand\purple[1]{{\color[rgb]{0.63515625, 0.49609375, 0.80859375}{#1}}}
\newcommand\white[1]{{\color{white}{#1}}}
\newcommand\orange[1]{{\color[rgb]{0.63515625, 0.51015625, 0.37734375}{#1}}}
$$

**潘逸文**
*<EMAIL>*

---
}
---
}
\item<+-> 这是一门 **<orange>数学课**：核心是数学，但是处处渗透物理思想
\item<+-> 同时也是一门 **<orange>语言课**：学会用复数说话，用方程描述物理，用特殊函数刻画物理演化
---
}
### 课程概况
\item<+-> 数学是一门独立的、有内在生命力、独特价值评价体系的学科
<div class='proof comment'>
**数学不只是工具**
数学为物理提供工具，但数学 **<red>不仅仅** 是物理的工具。
</div>
\item<+-> 特别地，数学是物理的 **<orange>语言**：没有数学，物理学家难以进行精确表达思想和传递信息
<div class='proof comment'>
**普世语言**
甚至可能是整个多重宇宙中所有智慧生物的普世语言。
</div>
---
}
### 课程概况
\item<+-> 反过来，物理也可以为数学反哺许多新奇的思想和生成数学结构
<div class='proof comment'>
**来自物理的反作用**
广义相对论与微分几何，散射振幅与代数几何，杨-米尔斯理论与微分拓扑，超对称与算子代数、代数簇
</div>
---
}
### 课程概况
\item<+-> 《数学物理方法》是众多后续课程和科研的基石
\item<+-> 电动力学、量子力学：数理方程的求解
统计力学：复变函数、级数、积分技巧
量子场论：复变函数、积分技巧、数理方程的求解
广义相对论：数理方程的求解
通信与信息技术：傅里叶级数、傅里叶变换，$\delta$ 函数
\item<+-> 现代理论物理可能需要远超本课程所涵盖的内容：微分与代数几何、抽象代数结构、复杂微分方程
---
}
### 课程概况
\item<+-> 数学物理方法：主要介绍两个基本要素
\item<+-> 复数与复变函数
\item<+-> 数理方程与特殊函数 (作为数理方程的解)
---
}
### 本章概要
\item 数的历史
\item 复数
\gray{复数、复数的表示方法、复数的性质}
\item 点集基础
\gray{内点、边界点、聚点、特殊点集}
\item 解析函数
\gray{复可导性、Cauchy-Riemann 条件、多值函数与支点}
{\setbeamertemplate{background}{![](image/section-title-pink.png)}%
---
}
\vspace{-0.8em}
\Huge{\color{white}{\textbf{{一}}}}
\vspace{0.7em}
\huge{\color{white}{\textbf{{数的历史}}}}
</center>}
---
}
### 自然数
\item<+-> 自然数 (natural numbers)，$\mathbb{N}$
\begin{align}
\orange{0}, 1, 2, 3, 4, \ldots
\end{align}
\item<+-> 用于标记现实物体、事件的数量
---
}
### 自然数
\item<+-> 原始社会用 **<orange>绳结** 计数
<center>
![width:400px](image/knots.jpg)
印加文明的记事绳结：奇普 (khipu)</center>---
}
### 自然数
\item<+-> 古埃及和巴比伦、古中国、古印度文明逐步出现 **<orange>书写符号** 来计数，包括阿拉伯数字
<center>
![height:240px](image/egyptian.png)
古埃及的数字符号 (一划，马蹄，绳索，睡莲，弯曲的手指，青蛙，Heh)</center>---
}
### 自然数
\item<+-> 现代社会发展出「正」字计数法
<center>
![height:300px](image/vote.jpg)
![height:300px](image/face.jpg)
</center>---
}
### 整数
\item<+-> 整数 (integers, ``whole'' in Latin)，$\mathbb{Z}$
\begin{align}
\ldots, -5, -4, -3, -2, -1, 0, 1, 2,3,4,5 , \ldots
\end{align}
<center>
![width:560px](image/-inf.jpg)
</center>---
}
### 整数
出现在一些早期文明中：古中国、古印度、古希腊
比较明确地提出整数的概念：毕达哥拉斯学派、欧几里得等学者
<center>
![height:240px](image/chinese1.png)
</center>

---

---
}
### 有理数
\item<+-> 有理数 (rational numbers)，$\mathbb{Q}$
\begin{align}
- \frac{3}{4}, \quad \frac{2021}{2022}, \quad \frac{2333}{666}, \quad - 3.1415926, \quad 4 , \quad \ldots \ .
\end{align}
\item<+-> 古希腊和印度科学家对有理数进行研究
---
}
### 无理数
\item<+-> 无理数 (irrational numbers)
\begin{align}
e, \qquad \pi, \qquad \sqrt{2}, \qquad \sqrt{3}, \qquad \ldots
\end{align}
---
}
### 无理数
\item<+-> 毕达哥拉斯学派的数学家 Hippasus 提出 $\sqrt{2}$ 是一种之前没有研究过的数：**<orange>无理数**。毕达哥拉斯认为「数是绝对的、万物皆数 (all is number)」，拒绝接受无理数的存在，对 Hippasus 处以 **<red>死刑** (推海里淹死)。
<center>
![height:240px](image/Hippasus.png)
</center>\item<+-> 公元前一世纪左右，《九章算术》记载了开方运算中出现的无理数
---
}
### 无理数
\item<+-> 在 16 世纪的欧洲，人们逐渐接受 **<orange>负数**、**<orange>分数**，到 18、19 世纪人们以 **<green>代数数** 和 **<green>超越数** 两种角度思考也研究无理数。
<div class='proof comment'>
**代数数**
即整系数多项式的根。
</div>
18, 19世纪数学家 Lambert、Legendre、Weierstrass、Cantor、Dedekind 等都对无理数进行深入的研究
---
}
### 实数
\item<+-> 实数 (real numbers), $\mathbb{R}$：有理数与无理数的并集
\item<+-> 中世纪时，阿拉伯数学家提出实数的概念
\item<+-> 「实 (real)」来自 17 世纪的笛卡尔
<div class='proof comment'>
**实根与虚根**
17 世纪的数学家已经在研究代数方程根，分为实根 (real roots) 和虚根 (imaginary roots)
</div>
---
}
### 实数
\item<+-> 有理数和无理数密密麻麻 **<orange>无缝** 形成实数轴 (real line)
<center>
![width:560px](image/real.png)
</center><div class='proof comment'>
**拓扑**
“密密麻麻、无缝”是一种拓扑概念
</div>
{\setbeamertemplate{background}{![](image/section-title-pink.png)}%
---
}
\vspace{-0.8em}
\Huge{\color{white}{\textbf{{二}}}}
\vspace{0.7em}
\huge{\color{white}{\textbf{{复数}}}}
</center>}
---
}
### 实数
\item<+-> 实数及其加、乘运算满足基本性质：对任意 $a, b \in \mathbb{R}$
\item<+-> 加法 **<orange>交换** 律 $a + b = b + a$，**<orange>结合** 律 $(a + b) + c = a + (b + c)$
\item<+-> 乘法 **<orange>交换** 律 $a \cdot b = b a$，**<orange>结合** 律 $(a \cdot b) \cdot c = a \cdot (b \cdot c)$
\item<+-> 加乘 **<orange>分配** 律：$a \cdot (b + c) = a b + a c$
\item<+-> 有加法 **<green>单位元** $0 \in \mathbb{R}$：$0 + a = 0 + a = a$
\item<+-> 有乘法 **<green>单位元** $1 \in \mathbb{R}$：$1 \cdot a = a \cdot 1 = a$
\item<+-> 有乘法 **<orange>零** 元 $0$：$a \cdot 0 = 0 \cdot a = 0$
\item<+-> 存在加法 **<green>逆元** $- a$：$(-a) + a = 0 $
\item<+-> 当 $a \ne 0$，存在乘法 **<green>逆元** $a^{-1}$：$a^{-1} \cdot a = 1 $
---
}
### 复数
\item<+-> 问题一：是否有别的非实数的东西满足上面这些运算性质？
\item<+-> 问题二：非常简单的实 (整) 系数代数方程 $x^2 + 1 = 0$ 是否有解？
\item<+-> 目标：扩充实数，使得
\item<+-> A: 维持实数的代数性质
\item<+-> B: 使得实方程 $x^2 + 1 = 0$ 有解
---
}
### 复数
\item<+-> 定义 **<green>虚数单位 (imaginary unit) $i$**，实现目标 B
<div class='proof equation'>
**虚数单位**
\begin{equation
</div>
i^2 + 1 = 0 \ .
\end{equation}
}
<div class='proof comment'>
**虚数单位**
有的文献和编程语言使用 $j$ 或者 $I$ 代替 $i$，
</div>
---
}
### 复数
\item<+-> 定义 **<green>复数 (complex numbers)** 为所有形如 $a + bi$ 的物体，其中 $a, b \in \mathbb{R}$
\item<+-> 对任意复数 $z = a + b i$，定义 **<green>实部 $\operatorname{Re**z \coloneqq a$}，**<green>虚部 $\operatorname{Im**z \coloneqq b$}
\item<+-> 全体复数形成的集合称为 **<green>复数集 $\mathbb{C**$}
---
}
### 复数的例子
\item<+-> **<orange>虚部为零** 的复数 $a + 0 i \in \mathbb{C}$ ($a \in \mathbb{R}$) 往往 **<orange>简写** 为 $a \coloneqq a + 0 i$。
<div class='proof comment'>
**子集关系**
实数集可以看成复数集的子集，$\mathbb{R
</div>
\subset \mathbb{C}$
}
\item<+-> **<orange>实部为零** 的复数 $0 + a i $ (其中 $a \in \mathbb{R}$) 的数称为 **<green>纯虚 (pure imaginary) 数**
\item<+-> 虚数单位 $0 + 1 i = i \in \mathbb{C}$
\item<+-> $0 - 1 i = - i \in \mathbb{C}$
\item<+-> 纯虚数 **<orange>简写** $0 + a i = a i \in \mathbb{C}$, $\forall a \in \mathbb{R}$
---
}
### 复数的例子
\item<+-> 对任意 $z = a + b i$ (其中 $a, b \in \mathbb{R}$)，定义其 **<green>复共轭 (complex conjugate)**
\begin{align}
\bar z = z^* = (a + bi)^* \coloneqq a - b i \ .
\end{align}
读作 z bar 或者 z star。
---
}
### 复数的四则运算
\item<+-> 数的最大价值在于运算
\item<+-> **<green>加法**：
\begin{align}
(a_1 + b_1 i) + (a_2 + b_2 i) \coloneqq (a_1 + a_2) + (b_1 + b_2)i
\end{align}
\item<+-> **<green>减法**：
\begin{align}
(a_1 + b_1 i) - (a_2 + b_2 i) \coloneqq (a_1 - a_2) + (b_1 - b_2)i
\end{align}
---
}
### 复数的四则运算
\item<+-> **<green>乘法**：
\begin{align}
(a_1 + b_1 i) \cdot (a_2 + b_2 i) \coloneqq (a_1 a_2 - b_1 b_2) + (a_1 b_2 + a_2 b_1)i \ .
\end{align}
<div class='proof comment'>
**减号**
注意结果的实部有个 **<orange>减号**
</div>
\item<+-> **<green>除法**：
\begin{align}
\frac{a_1 + b_1 i}{a_2 + b_2 i} \coloneqq \frac{
a_1 a_2 + b_1 b_2
}{a_2^2 + b_2^2} + \frac{- a_1 b_2 + a_2 b_1}{a_2^2 + b_2^2} i \ .
\end{align}

---

### 复数的四则运算：例子

* 考虑两个 **<orange>虚部为零** 的复数 $z_a \coloneqq a + 0 i$，$z_b \coloneqq b + 0 i$，其中 $a, b \in \mathbb{R}$
  \begin{align}
  z_a + z_b = (a + b) + (0 + 0) i = a + b \in \mathbb{R} \ .
  \end{align}

<div class='proof comment'>

**实数与复数的加法**

由此可见，虚部为零的复数加法跟实数加法是一模一样的。
</div>

---

### 复数的四则运算：例子

* 考虑一个虚部为零的复数 (其实就是实数) $z_a \coloneqq a + 0 i$，一个纯虚数 $z_b \coloneqq 0 + bi$，则它们的和
  \begin{align}
  z_a + z_b = (a + 0) + (0 + b)i = a + b i \in \mathbb{C}\ .
  \end{align}

<div class='proof comment'>

**复数的分解**

任何一个复数都可以分解为一个 **<orange>实数** 和一个 **<orange>纯虚数** 的和。
</div>

---

### 复数的代数运算

* 理解这些四则运算有两种思路。

* (1) 把这些 **<orange>具体的式子** 作为加减乘除运算的 **<orange>定义**，可以验证它们满足之前列举的所有性质 (交换律、结合律、分配律等)

* (2) 把运算应满足的 **<orange>规律** (交换律、结合律等) 作为出发点、**<orange>定义**，则上面这些具体的运算规则可以被推导出来，从而获得复数及其运算。

---

### 复数的代数运算

<div class='proof comment'>

**定义对象的新思路**

"It is **<red>not** who I am underneath, but **<orange>what I do**，that defines me."

——著名慈善家、企业家 Bruce Wayne
</div>

<center>

![width:720px](image/Bruce.jpg)
</center>

---

### 复数的代数运算

* 复数乘法定义实际上就是 **<orange>分配律**、**<orange>交换律**、**<orange>结合律** 的结果：把 $a_{1,2} + b_{1,2} i$ 均看成是 **<orange>实数** **<orange>加** **<orange>纯虚数**，
  \begin{align}
  (a_1 + b_1 i) (a_2 + b_2 i)
  = & \
  a_1 a_2 + b_1 i b_2 i + a_1 b_2 i + b_1 i a_2 \\
  = & \ a_1 a_2 + b_1 b_2 \orange{i^2} + (a_1 b_2 + b_1 a_2) i \\
  = & \ a_1 a_2 \orange{-} b_1 b_2 + (a_1 b_2 + b_1 a_2) i
  \end{align}

---

### 复数的代数运算

* $z$ 的模方 (modular-square)
  \begin{align}
  z \bar z = z z^* = (x + i y)(x - i y) = x^2 + y^2 \ .
  \end{align}

<div class='proof comment'>

**因式分解**

$x^2 + y^2$ 可以在复数范围内作因式分解：原本不可能的事情，在复数范围内变得可能。
</div>

* **<green>幂次运算**：对于 $n = 0, 1,2, 3, \dots$，
  \begin{equation}
  z^n = z \cdot z \cdot ... \cdot z \ .
  \end{equation}
* 常用结果：$i^{2k} = (-1)^k$，$i^{4k} = 1$, $k \in \mathbb{N}$

---

### 复数的表示

* **<orange>代数式表示/定义**：对任意 $z \in \mathbb{C}$，存在 $x, y \in \mathbb{R}$ 使得
  \begin{align}
  z = x + y i \ .
  \end{align}
* **<orange>矢量表示**：对任意 $z = x + yi \in \mathbb{C}$，可以在 $\mathbb{R}^2$ 上找到一个点/矢量 $(x,y)$ 对应

  允许矢量平移，矢量加减法 $=$ 复数加减法

<center>

![width:320px](image/vector-rep.jpg)
</center>

---

### 复数的表示

* **<orange>三角表示法**：用极坐标来标记矢量表示法中的 $x,y$，
  \begin{align}
  x = r \cos \theta, \quad y = r \sin \theta \qquad \Rightarrow
  \qquad
  z = & \ r \cos \theta + i r \sin \theta\\
  = & \ r(\cos\theta + i \sin \theta)\ .
  \end{align}
  角度 $\theta \in \mathbb{R}$ 称为 **<green>辐角 $\operatorname{arg} z$**，长度 $r$ 称为 **<green>模 (modulus) $|z|$**，也称为绝对值。
  \begin{align}
  r = |z| = \sqrt{x^2 + y^2} \ .
  \end{align}

<center>

![width:240px](image/Trig-Rep.png)
</center>

---

### 复数的表示

* **<orange>指数表示法**：**<green>$e^{i\theta} \coloneqq \cos \theta + i \sin \theta$**，从而
  \begin{align}
  z = r e^{i \theta} = r \exp \left[i \theta\right] \ .
  \end{align}

<center>

![width:400px](image/Exponential_Rep.png)
</center>

---

### 复数的表示

<div class='proof comment'>

**纯虚数的指数的理解**

注意 $\theta$ 是 **<orange>实数**，$i \theta$ 是 **<orange>纯虚数**。$i\theta$ 的指数应该通过 Taylor 展开，
</div>

\begin{align}
e^{i \theta} = \sum_{n = 0}^{+\infty} \frac{1}{n!} (i \theta)^n
= & \ \sum_{k = 0}^{+\infty} \frac{1}{(2k)!} (i \theta)^{2k}
+ \sum_{k = 0}^{+\infty} \frac{1}{(2k + 1)!} (i \theta)^{2k + 1}
\nonumber\\
= & \ \sum_{k = 0}^{+\infty} \frac{1}{(2k)!} i^{2k} \theta^{2k}
+ \sum_{k = 0}^{+\infty} \frac{1}{(2k + 1)!} i^{2k + 1} \theta^{2k + 1}\nonumber\\
= & \ \sum_{k = 0}^{+\infty} \frac{1}{(2k)!} (-1)^k  \theta^{2k}
+ i \sum_{k = 0}^{+\infty} \frac{1}{(2k + 1)!} (-1)^k \theta^{2k + 1}\nonumber\\
= & \ \cos \theta + i \sin \theta \ .
\end{align}

---

### 复数的表示：辐角多值性

* 三角表示和指数表示中都涉及 **<orange>辐角**。
* 给定一个非零 $z$，其辐角 **<red>不唯一**。对任意 **<green>整数 $k$**，都有
  \begin{align}
  z = r (\cos \theta + i \sin \theta)
  = r (\cos (\theta + 2\pi k) + i \sin (\theta + 2\pi k)) \ .
  \end{align}
  因此，指数表达式也不唯一，
  \begin{align}
  z = re^{i \theta} = r e^{i (\theta + 2\pi k)} \ .
  \end{align}
* 有时候，人们会 **<orange>人为限制** $\theta$ 的一个取值范围，如 $\theta \in [0, 2\pi)$

---

### 复数的表示：例子

* $z = 3 + 4 i$，则利用 $r = \sqrt{3^2 + 4^2} = 5$, $\tan \theta = 4/3$
  \begin{align}
  z = & \ 5 \cos \left(\operatorname{arctan}\frac{4}{3}\right)
  + 5 \sin \left(\operatorname{arctan}\frac{4}{3}\right) i\\
  = & \ 5 \exp \left(i \arctan \frac{4}{3}\right) \ .
  \end{align}

<center>

![height:240px](image/example-1.pdf)
</center>

---

### 复数的表示：例子

* $z = 0 + 3i$，则
  \begin{align}
  z = 3 \cos \frac{\pi}{2} + 3 i \sin \frac{\pi}{2} = 3 e^{i \frac{\pi}{2}} \ .
  \end{align}

---

### 复数的表示：特殊值

* $-1$ 的四种写法
  \begin{align}
  -1 = - 1 + 0 i  =  \cos \pi + i \sin \pi = e^{\pi i} \ .
  \end{align}
  移项：**<orange>欧拉公式**
  \begin{align}
  e^{\pi i} + 1 = 0 \ .
  \end{align}
* $+1$
  \begin{align}
  e^{2\pi i} = \cos 2\pi + i \sin 2\pi = 1 + 0 i = 1\ .
  \end{align}

---

### 复数的表示：特殊值

* 虚数单位 $i$，
  \begin{align}
  i = 0 + i = \cos \frac{\pi}{2} + \sin \frac{\pi}{2} i = e^{ i \frac{\pi}{2}}  \ .
  \end{align}
* 负虚数单位 $- i$，
  \begin{align}
  - i = 0 - i = \cos ( - \frac{\pi}{2}) + \sin (-\frac{\pi}{2}) i = e^{ - i \frac{\pi}{2}} \ .
  \end{align}

---

---
}
### 指数表示下的乘除运算
\item<+-> 在代数式表示法中，乘法和除法的表达式都非常复杂。
\item<+-> **<orange>定理**：考虑 $z_1 = r_1 e^{i \theta_1}$，$z_2 = r_2 e^{i \theta_2}$。则
\begin{align}
z_1 z_2 = r_1 r_2 e^{i (\theta_1 + \theta_2)}, \qquad
\frac{z_1}{z_2} = \frac{r_1}{r_2} e^{i (\theta_1 - \theta_2)} \ .
\end{align}
<div class='proof'>
**证明**
利用三角表示法：$z_{1,2
</div>
= r_1 (\cos\theta_{1,2} + i \sin \theta_{1,2})$，得到
\begin{align}
z_1 z_2
= & \ r_1 r_2 (\cos \theta_1 + i \sin \theta_1)(\cos \theta_2 + i \sin \theta_2) \nonumber\
= & \ \visible<+->{r_1 r_2 (\cos \theta_1 \cos\theta_2 - \sin \theta_1 \sin\theta_2
+ i \sin \theta_1 \cos \theta_2 + i \cos \theta_1 \sin \theta_2
)} \nonumber \ .
\end{align}
}
---
}
### 指数表示下的乘除运算
<div class='proof'>
**证明**
利用三角函数的积化和差，得到
\begin{align
</div>
z_1 z_2
= & \ r_1 r_2 [\cos (\theta_1 + \theta_2) + i \sin (\theta_1 + \theta_2)]
= r_1 r_2 e^{i (\theta_1 + \theta_2)} \ .
\end{align}
}
---
}
### 指数表示下的乘除运算
<div class='proof'>
**证明**
对于除法，
\begin{align
</div>
\frac{z_1}{z_2} = & \ \frac{r_1 \cos \theta_1 + i r_1 \sin \theta_1}{r_2 \cos \theta_2 + i r_2 \sin \theta_2}\
= & \ \frac{r_1 r_2 \cos\theta_1 \cos \theta_2 + r_1 r_2 \sin\theta_1 \sin \theta_2}{r_2^2\cos^2\theta_2 + r_2^2\sin^2\theta_2} \
& \ + \frac{- r_1 r_2 \cos\theta_1 \sin \theta_2 + r_1 r_2 \sin\theta_1 \cos \theta_2}{r_2^2\cos^2\theta_2 + r_2^2\sin^2\theta_2}\
= & \ \frac{r_1 r_2 \cos(\theta_1 - \theta_2)}{r_2^2}
+ \frac{r_1 r_2 \sin (\theta_1 - \theta_2)}{r_2^2} = \frac{r_1}{r_2} e^{i (\theta_1 - \theta_2)} \ .
\end{align}
}
---
}
### 总结
\item<+-> 三种常用表示：代数表示、三角表示、指数表示
\begin{align}
z = x + i y = r \cos\theta + i r \sin \theta = r e^{i \theta} \ .
\end{align}
\item<+-> 模长 $|z|$
\begin{align}
|z| = r = \sqrt{(r \cos\theta)^2 + (r \sin \theta)^2} = \sqrt{x^2 + y^2}
= \sqrt{ z \bar z}
\end{align}
模方 (modulus squared)
\begin{align}
|z|^2 = z \bar z \ .
\end{align}
---
}
### 总结
\item<+-> $| - z| = |z|$
\item<+-> $|e^{i \theta}| = 1$，其中 $\theta \in \mathbb{R}$
\item<+-> $|z_1 z_2| = |z_1| |z_2|$
---
}
### 代数基本定理
\item<+-> **<orange>代数基本定理**：任何一个 $n$-次复系数多项式都有 $n$ 个复数根
<div class='proof comment'>
**重根**
这 $n$ 个复数根可能有 **<orange>重复**。
</div>
<div class='proof comment'>
**因式分解**
$n$-次复系数多项式 $a_n x^n + a_{n - 1
</div>
z^{n - 1} + ... + a_0$ 一定可以分解为
\begin{equation}
P(z) = a_n(z - z_1) (z - z_2) \cdots (z - z_n) \ ,
\end{equation}
其中 $z_i$ 为根。
}
---
}
### 代数基本定理
\item<+-> **<orange>韦达定理**：对任意复多项式 $P(z) = a_n z^n + a_{n - 1}z^{n - 1} + \cdots + a_0$ 的根 $z_1, \cdots, z_n$ 满足
\begin{equation}
(-1)^n a_n \prod_{k = 1}^{n}z_k = a_0, \qquad
a_n\sum_{k = 1}^{n}z_k = - a_{n - 1} \ .
\end{equation}
---
}
### 无穷远点
\item<+-> $\mathbb{R}$ 与无穷远点
<center>
\href{./animation/ExtendedR1.html}{
![width:480px](animation/ExtendedR1.png)
}
</center>\item<+-> $\mathbb{R} \cup \infty$ 实际上就是一维圆圈 $S^1$。
---
}
### 无穷远点
\item<+-> $\mathbb{C}$ 与无穷远点 $\infty$ 合并为 **<green>扩充的复平面**
\item<+-> $\mathbb{C} \cup \{\infty\}$ 实际上就是二维球面 $S^2$。
---
}
<center>
\includegraphics{image/extended-C.pdf}
</center>{\setbeamertemplate{background}{![](image/section-title-pink.png)}%
---
}
\vspace{-0.8em}
\Huge{\color{white}{\textbf{{三}}}}
\vspace{0.7em}
\huge{\color{white}{\textbf{{点集基础}}}}
</center>}
---
}
### 点集基础
\item<+-> 复变函数的定义域是 $\mathbb{C}$ 中的点集
\item<+-> 需要清楚 $\mathbb{C}$ 中点集的性质
\item<+-> 常用符号
s.t. = 「使得」
$\forall$ (Any) = 「任意」
$\exists$ (Exists) = 「存在」
$\in$ = 「属于」，$\subset$ = 是子集
$\epsilon$ = 「希腊字母 epsilon」
---
}
### 邻域
\item<+-> **<green>定义**：以 $z_0$ 为中心半径为 $r > 0$ 的 **<green>邻域 (neighborhood)**
\begin{align}
N(z_0, r) \coloneqq \{z \in \mathbb{C} \ | \ |z - z_0| < r\} \ .
\end{align}
\item<+-> 直观上是以 $z_0$ 为中心 $r$ 为半径的 **<orange>开** 圆盘
<center>
\includegraphics{image/open-disk.pdf}
</center>\item<+-> 也可以用其他 **<orange>多边形** 的 **<orange>内部** 作为定义：$\triangle$, $\blacksquare$, ...
\item<+-> 是下面所有讨论的基础
---
}
### 点与点集的相对关系：内点
\item<+-> 下面讨论点与点集的 **<orange>相对关系**
---
}
### 点与点集的相对关系：内点
\item<+-> **<green>定义**：考虑集合 $S \subset \mathbb{C}$，以及点 $z_0 \in \mathbb{C}$。倘若 **<orange>存在** $\epsilon > 0$ 使得 $N(z_0, \epsilon) \subset S$，则称 $z_0$ 为 $S $ 的一个 **<green>内点**
<center>
\includegraphics{image/inner-point.pdf}
</center>

---

---
}
### 点与点集的相对关系：内点
\item<+-> **<green>定义**：考虑集合 $S \subset \mathbb{C}$，以及点 $z_0 \in \mathbb{C}$。倘若 **<orange>存在** $\epsilon > 0$ 使得 $N(z_0, \epsilon) \subset S$，则称 $z_0$ 为 $S $ 的一个 **<green>内点**
\visible<+->{
<div class='proof comment'>
**$z_0 \in S$**
由于 $z_0$ 必然属于 $N(z_0, \epsilon)$，因此，当 $z_0$ 是 $S$ 的内点时，必然有 $z_0 \in S$。
</div>
}
\visible<+->{
<div class='proof comment'>
**包含与包围**
内点大致刻画一个点被一个点集 **<orange>包含** 且 **<orange>紧密包围**
</div>
}
---
}
### 点与点集的相对关系：内点
<div class='proof comment'>
**内点例子**
若 $z \in N(z_0, r)$，则 $z$ 是 $N(z_0, r)$ 的内点。
</div>
<div class='proof comment'>
**内点判断**
考虑点集 $S \coloneqq \{z \in \mathbb{C
</div>
\ | \ |z| \le 1\}$。考虑
(1) $z_0 = 0.9999 \ldots 9$ (有限个 9)
(2) $z_0 = 0.9999 \ldots $ (无限个 9)
}
<div class='proof comment'>
**内点判断**
考虑点集 $S \coloneqq \{z \in \mathbb{C
</div>
\ | \ 0 < |z| < 1\}$。考虑
(1) $z_0 = 0$ \ \ \ (2) $z_0 = 1$ \ \ \ (3) $z_0 = e^{\pi i /3}$
}
---
}
### 点与点集的相对关系：边界点
\item<+-> **<green>定义**：考虑点集 $S \subset \mathbb{C}$，$z_0 \in \mathbb{C}$。若对 $\forall \epsilon > 0$，均有
\begin{align}
N(z_0, \epsilon) \cap S \ne \emptyset \ (\text{有交}) \ , \qquad
N(z_0, \epsilon) \not\subset S \ (\text{不被包含}) \ ,
\end{align}
则称 $z_0$ 为 $S$ 的 **<green>边界点**。
<div class='proof comment'>
**形象理解**
点集 $S$ 的边界点 $z_0 $ 与点集 $S$ **<orange>若即若离**
\begin{center
</div>
![height:180px](image/touch.jpg)
</center>}
---
}
### 点与点集的相对关系：边界点
\item<+-> **<green>定义**：考虑点集 $S \subset \mathbb{C}$，$z_0 \in \mathbb{C}$。若对 $\forall \epsilon > 0$，均有
\begin{align}
\text{相交非空}~N(z_0, \epsilon) \cap S \ne \emptyset \ , \quad
\text{不完全包含}~N(z_0, \epsilon) \not\subset S \ ,
\end{align}
则称 $z_0$ 为 $S$ 的 **<green>边界点**。
<div class='proof comment'>
**边界点的从属关系**
若 $z_0$ 是 $S$ 的边界点，则 $z_0$ 可能 **<orange>属于** 也可能 **<orange>不属于** $S$。这跟 **<orange>内点** 是 **<red>不一样** 的。
</div>
---
}
### 点与点集的相对关系：边界点
\item<+-> 一个点集 $S$ 的全体边界点所构成的集合称为 $S$ 的 **<green>边界**，记作 **<green>$\partial S$**。
---
}
### 点与点集的相对关系：边界点
<div class='proof comment'>
**边界点例子**
考虑点集 $S \coloneqq \{z \in \mathbb{C
</div>
\ | \ 0 < |z| < 1\}$。则原点 $z = 0$ 是 $S$ 的边界点。
<center>
\includegraphics{image/boundary-point-example.pdf}
</center>}
---
}
### 点与点集的相对关系：边界点
<div class='proof comment'>
**曲线的边界**
考虑一条连续的曲线 $C \subset \mathbb{C
</div>
$，则其边界是其自身 $\partial C = C$。
<center>
\includegraphics{image/boundary-point-example-2.pdf}
</center>}
---
}
### 点与点集的相对关系：边界点
<div class='proof comment'>
**孤立点的边界**
任何一个点 $z$ 可以构成一个点集 $S = \{z
</div>
$。其边界为 $\partial S = \{z\}$。
<center>
\includegraphics{image/isolated-point.pdf}
</center>}
---
}
### 点与点集的相对关系：例子
\item<+-> 设 $S \coloneqq \{|z| \le 1\}$。
(1) 则 $z = 1$ 是 \visible<+->{**<orange>边界点**}
(2) 则 $z = 0$ 是 \visible<+->{**<orange>内点**}
\item<+-> 设 $S \coloneqq \{|z| \le 1\}$。则 $\partial S = $
\visible<+->{\begin{equation}
\{|z| = 1\}, \qquad \text{or} \qquad
\{e^{i \theta} \ | \ \theta \in \mathbb{R}\} \ .
\end{equation}}
\item<+-> 设 $S \coloneqq \{|z| < 1\}$。则 $\partial S = $
\visible<+->{\begin{equation}
\{|z| = 1\}, \qquad \text{or} \qquad
\{e^{i \theta} \ | \ \theta \in \mathbb{R}\} \ .
\end{equation}}
---
}
### 点与点集的相对关系：聚点
\item<+-> **<green>定义**：设 $S \subset \mathbb{C}$，$z_0 \in \mathbb{C}$。若对 $\forall \epsilon > 0$ 始终有
\begin{align}
\text{相交非空} ~ & N(z_0, \epsilon) \cap S \ne \emptyset \
& N(z_0, \epsilon) \cap S \ \text{包含 「$z_0$ 以外的点」} \ ，
\end{align}
则称 $z_0$ 为 $S$ 的一个 **<green>聚点**。
<div class='proof comment'>
**从属关系**
聚点 $z_0$ 可能 **<orange>属于** 也可能 **<orange>不属于** $S$。
</div>
---
}
### 点与点集的相对关系：聚点
<div class='proof comment'>
**无限逼近**
说明 $z_0$ 能够被 **<orange>$S$ 的点** **<orange>无限逼近**。
\begin{center
</div>
![width:560px](image/聚点-2.png)
</center>}
---
}
### 点与点集的相对关系：聚点
<div class='proof comment'>
**无限逼近**
说明 $z_0$ 能够被 $S$ 的点 **<orange>无限逼近**，在 $z_0$ 任意近的地方都有 $S$ 的人。
\begin{figure
</div>
\includegraphics{image/approaching.pdf}
</center>}
---
}
### 点与点集的相对关系：例子
\item<+-> 设 $z \in N(0,1)$，**<orange>$z \ne 0$**。
(1) $z$ 是 $N(0,1)$ 的什么点？\visible<+->{**<orange>内点，也是聚点**}
(2) 设 $S \coloneqq \{z^n \ | \ n \in \mathbb{N}\}$。则原点 $z = 0$ 是 $S$ 的什么点？\visible<+->{**<orange>聚点**}
\visible<+->{
<div class='proof'>
**证明**
由题目知道 $0 < |z| < 1$。因此随着 $n \to +\infty$，$|z|^n \to 0$。
换言之，对 $\forall \epsilon > 0$，可以选择一个足够大的 $n$，
\begin{align
</div>
n > \frac{\ln \epsilon}{\ln |z|} \qquad \Rightarrow \qquad |z^n| = |z|^n < \epsilon \ .
\end{align}
于是 $|z^n - 0| < \epsilon$，$z^n \in N(0, \epsilon)$，因此 $N(0, \epsilon) \cap S$ 非空。且该交集必然有 $0$ 外的点 (比如 $z^n$)。得证。
}
}
---
}
### 点与点集的相对关系：例子
\item<+-> 设 $z \in \partial N(0, 1)$。考虑 $S = \{\frac{1}{n}z^n \ | \ n \in \mathbb{N}\}$。原点是否 $S$ 的聚点？\visible<+->{**<orange>是**}
\item<+-> 设 $z \in \partial N(0,1)$。考虑 $S = \{z^n \ | \ n \in \mathbb{N}\}$。原点是否 $S$ 的聚点？\visible<+->{**<orange>不是**}
\item<+-> 设 $z \in \partial N(0, \frac{1}{2})$。考虑 $S = \{z^n \ | \ n \in \mathbb{N}\}$。原点是否 $S$ 的聚点？\visible<+->{**<orange>是**}
\item<+-> 考虑点集 $S = \{\frac{1}{n}e^{\frac{\pi i}{n}} \ | \ n \in \mathbb{N}\}$。则原点是否 $S$ 的聚点？
\visible<+->{**<orange>是**}
---
}
### 点与点集的相对关系：总结
\item<+-> 内点、边界点、聚点均刻画点与点集的相对关系
\item<+-> 三种点的关系如下
\vspace{1em}
<center>
\includegraphics{image/three-types-of-points.pdf}
</center>

---

---
}
### 点与点集的相对关系：例子
\item<+-> 下面 A, B, C, D 是点集 $S$ 的什么点？
<center>
\includegraphics{image/lolipop.pdf}
</center>---
}
### 特殊点集
\item<+-> **<green>定义**：设 $S\subset \mathbb{C}$，若 $\forall z \in S$ **<orange>均** 为 $S$ 的 **<orange>内点**，则称 $S$ 为 **<green>开集**
<div class='proof comment'>
**开集的意义**
开集用于刻画、标记 **<orange>邻近关系**。
\begin{figure
</div>
\includegraphics{image/significance-of-open-set.pdf}
</center>}
---
}
### 特殊点集
\item<+-> 邻域是特殊的开集
\item<+-> 开集的具体定义依赖 **<orange>邻域** 本身：倘若邻域使用 **<orange>别的形状** 定义，则开集的具体概念也会跟着改变
\item<+-> 开集公理 $\Rightarrow$ **<green>拓扑结构**
(1) 有限或无限个开集的 **<orange>并** 还是 **<orange>开集**
(2) 有限个开集的 **<orange>交** 还是 **<orange>开集**
(3) $\mathbb{C}$ 与 $\emptyset$ 是 **<orange>开集**
---
}
### 特殊点集
\item<+-> 存在其他满足开集公理的“开集概念” (拓扑结构)
<div class='proof comment'>
**离散拓扑**
当前的开集概念：任何一个点 $z$ 的任意邻域中都有 **<orange>无数个邻居**——**<orange>非孤立性**。
**<orange>离散拓扑**：任何一个点 $z$ 都存在一个 **<orange>最小邻域**，该邻域只包含它自己——**<orange>孤立性**
</div>
---
}
### 特殊点集
\item<+-> **<green>定义**：若开集 $U$ 内任何两点都可以用一条 **<orange>完全** 属于该集合的曲线连接，则称该开集 $U$ 是 **<green>连通** 的。
\item<+-> 非空、**<orange>连通** 的开集称为 **<green>区域 (domain)**
<center>
\includegraphics{image/domain.pdf}
</center>---
}
### 特殊点集
\item<+-> **<green>定义**：区域 $D$ 及其边界 $\partial D$ 的并集称为一个 **<orange>闭域** 或 **<green>$D$ 的闭包**，记作 $\bar D$。
\item<+-> **<green>定义**：若区域 $D$ 内任何 **<orange>简单闭曲线** 的 **<orange>内部** 均属于 $D$，则称 $D$ 是 **<green>单连通** 的，否则称为 **<green>复连通**
<center>
\includegraphics{image/simply-connected.pdf}
</center>{\setbeamertemplate{background}{![](image/section-title-pink.png)}%
---
}
\vspace{-0.8em}
\Huge{\color{white}{\textbf{{四}}}}
\vspace{0.7em}
\huge{\color{white}{\textbf{{解析函数}}}}
</center>}
---
}
### 复变函数定义
\item<+-> 设 $S \subset \mathbb{C}$。若对 $\forall z \in S$，按照某个 **<orange>规则 $f$** 有 **<orange>唯一** 的复数 $w$ 与之对应，则 $f$ 定义了一个 **<green>单值复变函数**，定义域为 $S$。
\item<+-> 设 $S \subset \mathbb{C}$。若对 $\forall z \in S$，按照某个 **<orange>规则 $f$** 有 **<orange>一个或多个** 的复数 $w$ 与之对应，则 $f$ 定义了一个 **<green>多值复变函数**，定义域为 $S$。
\item<+-> 不管是单值还是多值复变函数，都简记为
\begin{align}
w = f(z) \qquad
\text{or}
\qquad
f(z)\ .
\end{align}
---
}
### 复变函数定义
\item<+-> 记 $w = u + i v$，$z = x + i y$，则
\begin{align}
w = f(z) = u(z) + i v(z) = u(x, y) + i v(x, y) \ .
\end{align}
\visible<+->{换言之，一个复变函数相当于 **<orange>两个** **<orange>二元实函数**，分别称为 $f$ 的实部 $\operatorname{Re} f$ 和虚部 $\operatorname{Im} f$。}
---
}
### 极限
\item<+-> **<green>定义**：设 $w = f(z)$ 的 **<orange>定义域是 $S$**，设某点 $z_0$ 为 $S$ 的 **<orange>聚点**。
若 $\exists w_0 \in \mathbb{C}$，s.t. \visible<+->{对 **<orange>$\forall \epsilon > 0$**，}
\visible<+->{都 **<green>$\exists \delta > 0$**}
\visible<+->{s.t. 对 $\forall z \in (N(z_0, \delta) \cap S) - \{z_0\}$} \visible<+->{都有 $|f(z) - w_0| < \epsilon $,}
\visible<+->{则称 $f$ 在 $z_0$ 处的 **<green>极限存在**，并称 $w_0$ 是 $f(z)$ 在 $z_0$ 处的 **<green>极限**，记作 $w_0 = \lim_{z \to z_0} f(z)$。}
\vspace{-0.5em}
<center>
![width:280px](image/limit.png)
![width:280px](image/dio.png)
</center>---
}
### 极限
<div class='proof comment'>
**翻译翻译**
\begin{center
</div>
![width:480px](image/translate.jpeg)
</center>**<orange>极限存在** 就是，对于 **<orange>甲方** 给出的任意小 **<orange>精度要求 $\epsilon$**，我们总能 **<orange>在定义域 $S$ 中** 找到一个 **<orange>足够小的距离 $\delta$**，使得 **<orange>距离以内** 的所有函数值与目标值 $w_0$ 的差别 **<orange>小于** 精度要求 $\epsilon$。
}
---
}
### 极限
<div class='proof comment'>
**$f$ 在 $z_0$ 可以没有定义**
\visible<+->{根据设定 $z_0$ 是 $S$ 的 **<orange>聚点**，而 $S$ 的聚点可以 **<orange>属于** 也可能 **<red>不属于** $S$，因此 $z_0$ 可能不属于 $f$ 的定义域。
</div>
}
<div class='proof comment'>
**$z$ 的地位**
\visible<+->{条件 $\forall z \in (N(z_0, \delta) \cap S) - \{z_0
</div>
$ 强调两件事}
\visible<+->{(1) $z$ 应该在 $f$ 的定义域中，且应该与 $z_0$ 靠得很近 (距离小于 $\delta$)}
\visible<+->{(2) $z \red{\ne} z_0$ }
}
---
}
### 极限
<div class='proof comment'>
**逼近的方向**
若 $S$ 可以从 **<orange>多个方向** 逼近 $z_0$，则极限存在要求 $f$ 在 **<orange>所有** 逼近方向都存在且趋于 **<orange>同一个值 $w_0$**：四面八方极限存在，且四面八方极限相等。这比一元函数的极限存在要 **<red>苛刻得多**。
</div>
---
}
### 连续性
\item<+-> **<green>定义**：设复变函数 $f$ 的定义域是 $S$。考虑 $z_0\in S$，且是 $S$ 的 **<orange>聚点**。
若
\begin{equation}
\lim_{z \to z_0} f(z) \ \text{存在}, \qquad
\text{且} \qquad
\lim_{z \to z_0} f(z) = f(z_0) \ ,
\end{equation}
则称 $f$ 在 $z_0$ 处 **<green>连续**。
<div class='proof comment'>
**$f(z_0)$ 有定义**
由设定 $z_0 \in S$，$f$ 在 $z_0$ 是 **<orange>有定义** 的。连续性要求这个值恰好等于 $f(z)$ 趋于 $z_0$ 的极限。
</div>
\item<+-> 若 $f$ 在 $S$ 中每一点都连续，则称 $f$ 在 **<green>$S$ 上连续**。
---
}
### 可导性
\item<+-> 设 $f$ 定义在 **<orange>区域 $D$** 上。$z_0 \in D$。若
\begin{align}
\lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z} \text{ 存在且有限} \ ,
\end{align}
则称 $f$ 在 $z_0$ 处 **<green>可导** 或 **<green>可微**，而该极限称为 $f$ 在 $z_0$ 处的 **<green>导数** 或者 **<green>微商**，记作
\begin{align}
[f'(z)]_{z = z_0}, \qquad
f'(z_0), \qquad
\frac{df(z)}{dz}\bigg|_{z = z_0}, \qquad
\frac{d}{dz}\bigg|_{z = z_0}f(z) \ .
\end{align}
<div class='proof comment'>
**区域**
定义域是个区域，因此 当 $|\Delta z|$ 足够小的话可以保证 $z + \Delta z \in D$，从而 $f(z + \Delta z)$ **<orange>有定义**。
</div>

---

---
}
### 解析函数
\item<+-> 倘若函数 $f$ 在 **<orange>区域 $D$** 内处处可导，则称 $f$ 是 $D$ 上的 **<green>解析 (analytic) 函数**，或者 **<green>全纯 (holomorphic) 函数**，或者称 $f$ 在 $D$ 内 **<green>解析**。
<div class='proof comment'>
**解析**
解析这个形容词是针对 **<orange>一个区域** 的概念，是对函数在 **<orange>一定范围** 内的行为进行约束，并非一点处的概念，**<red>没有**「在一点处解析」的说法。
有时人会 **<orange>偷懒** 说「在一点处解析」，但实际意思是指在 **<orange>点的邻域** 内解析。
</div>
---
}
### 可导性：例
\item<+-> $f(z) = z^n$，$n \in \mathbb{N}$，则可以直接计算得到
\begin{align}
\lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z} = n z^{n - 1}\ .
\end{align}
因此这个 $f$ 在 $\mathbb{C}$ 上 **<orange>处处可导**。
---
}
### 可导性：例
\item<+-> $f(z) = \bar{z}$。因此 $f(z = x + i y) = x - i y$
\visible<+->{{\small\begin{align}
\lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z} = \lim_{\substack{\Delta x \to 0 \\ \Delta y \to 0}} \frac{x + \Delta x - i (y + \Delta y) - (x - i y)}{\Delta x + i \Delta y} \ .
\end{align}}}
\visible<+->{若 $\Delta z$ 沿着 **<orange>实轴** 逼近 $0$，则 $\Delta y = 0$，
\begin{align}
\lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z}
= \lim_{\substack{\Delta x \to 0 \\ \Delta y \to 0}} \frac{\Delta x }{\Delta x} = \orange{1} \ .
\end{align}}
\visible<+->{若 $\Delta z$ 沿着 **<orange>虚轴** 逼近 $0$，则 $\Delta x = 0$，
\begin{align}
\lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z}
= \lim_{\substack{\Delta x \to 0 \\ \Delta y \to 0}} \frac{ - i \Delta y }{i \Delta y} = \red{- 1} \ .
\end{align}}
---
}
### 可导性：例
\item<+-> 极限不存在，因此 $f(z) \coloneqq \bar z$ **<red>处处不可导**。
---
}
### 解析函数
\item<+-> **<green>定义**：倘若 $f$ 在 $z_0$ 处不解析 (包括没有定义)，但是 $f$ 在 $z_0$ 的任意邻域内 **<orange>都有** 解析点，则称 $z_0$ 为 $f$ 的 **<green>奇点 (singularity)**。
<div class='proof comment'>
**奇点的含义**
不同领域对奇点的定义和理解会有所不同。这里是复分析中比较常用的一种理解。
</div>
% \item<+-> **<green>定义**：设 $D$ 是区域，若 $f$ 在 $D$ 中除了若干孤立的点 **<orange>以外** 处处解析，有时会强调 $f$ 是 $D$ 上的 **<green>亚纯 (meromorphic) 函数**。
%
<div class='proof comment'>
**本课程不区分亚纯全纯**
% 本课程后续讨论不会使用亚纯这一术语。
%
</div>
---
}
### 求导法则
\item<+-> 求导法则
(1) 线性性：$[f(z) + g(z)]' = f'(z) + g'(z)$
(2) 莱布尼兹 (Leibniz) 律：$[f(z)g(z)]' = f'(z)g(z) + f(z) g'(z)$
(3) 链式法则 (chain rule)：$f(g(z))' = \frac{df}{dg} \frac{dg}{dz}$
\item<+-> 常见复解析函数导数与实光滑函数的导数在形式上是一样的。
---
}
### Cauchy-Riemann 条件
\item<+-> **<orange>复可导性** 是很 **<orange>强** 的条件，比之前学的实变可导性要强得多：要求极限与 $\Delta z \to 0$ 的 **<orange>方向无关**
<center>
![height:360px](image/mikasa.jpg)
</center>---
}
### Cauchy-Riemann 条件
\item<+-> 求导：$f(z = x + i y) = u(x, y) + i v(x, y)$
\begin{align}
f'(z)
= \lim_{\substack{\Delta x \to 0\\\Delta y \to 0}}\bigg[ & \ \frac{u(x + \Delta x, y + \Delta y) + i v(x + \Delta x, y + \Delta y)}{\Delta x + i \Delta y}\
& \ \qquad \qquad\qquad \qquad \qquad - \frac{u(x, y ) + i v(x, y )}{\Delta x + i \Delta y} \bigg] \ . \nonumber
\end{align}
\item<+-> 考虑实轴和虚轴方向
---
}
### Cauchy-Riemann 条件
\item<+-> 考虑 $\Delta z$ 从 **<orange>实轴** 趋近零：**<orange>$\Delta y = 0$**，
\begin{align}
f'(z) = \frac{\partial u}{\partial x} + i \frac{\partial v}{\partial x} \ .
\end{align}
\item<+-> 考虑 $\Delta z$ 从 **<orange>虚轴** 趋近零：**<orange>$\Delta x = 0$**，
\begin{align}
f'(z) = \frac{\partial v}{\partial y} - i \frac{\partial u}{\partial y} \ .
\end{align}
\item<+-> 二者应该相等 (方向无关)：**<green>Cauchy-Riemann 条件**
\begin{align}
& \ \frac{\partial u}{\partial x} + i \frac{\partial v}{\partial x}
= \frac{\partial v}{\partial y} - i \frac{\partial u}{\partial y}\
\Rightarrow & \ \green{
\frac{\partial u}{\partial x} = \frac{\partial v}{\partial y}, \qquad
\frac{\partial u}{\partial y} = - \frac{\partial v}{\partial x}} \ .
\end{align}
---
}
### Cauchy-Riemann 条件的复坐标表示
\item<+-> **<orange>定理**：实可微性 + CR 条件 $\Leftrightarrow$ 复可导性
\item<+-> **<orange>定理**：实连续一阶偏导 + CR 条件 $\Leftrightarrow$ 复可导性
\item<+-> 通常，物理中的函数都至少具有上述 **<orange>实** 可导性质。以后统称为 **<green>足够光滑、性质良好** 的函数。
---
}
<div class='proof comment'>
**实可微性**
二元函数 $f(x,y)$ 在 $(x_0, y_0)$ 实可微指的是
\begin{equation
</div>
\lim_{\substack{\Delta x \to 0 \\ \Delta y \to 0}} \frac{[f(x + \Delta x, y + \Delta y) - f(x_0, y_0) -  \partial_x f \Delta x - \partial_y f \Delta y]_{(x_0, y_0)}}{\sqrt{\Delta x^2 + \Delta y^2}} = 0 \nonumber
\end{equation}
偏导数存在但不可微的例子
\begin{equation}
\sqrt{xy}, \qquad \left\{\begin{array}{cc}
0 & (0,0)\
\frac{y^3}{x^2 + y^2} & (x, y) \neq (0,0)
\end{array}\right.
\end{equation}
}
---
}
### Cauchy-Riemann 条件的复坐标表示
\item<+-> $z = x + i y$，$\bar z = x - i y$
\item<+-> 反解
\begin{align}
x = \frac{1}{2}(z + \bar z), \qquad
y = \frac{1}{2i}(z - \bar z) \ .
\end{align}
\item<+-> 任何关于 $z, \bar z$ 的函数都可以重新表达为 $x, y$ 的函数，反之亦然，
\begin{align}
f(z, \bar z) = & \ f(x + i y, x - i y),\
f(x, y) = & \ f\left(\frac{1}{2}(z + \bar z), \frac{1}{2i}(z - \bar z)\right) \ .
\end{align}
---
}
### Cauchy-Riemann 条件的复坐标表示
\item<+-> **<green>定义** 复偏导符号 $\frac{\partial}{\partial z}, \frac{\partial}{\partial \bar z}$
\begin{align}
\frac{\partial f}{\partial z} = \partial_z f \coloneqq \frac{1}{2} \left(\frac{\partial f}{\partial x} \green{-} i \frac{\partial f}{\partial y}\right) \ ,\
\frac{\partial f}{\partial \bar z} = \partial_{\bar z} f \coloneqq \frac{1}{2} \left(\frac{\partial f}{\partial x} \green{+} i \frac{\partial f}{\partial y}\right)\ .
\end{align}
<div class='proof comment'>
**链式法则**
上述求导算符可以通过「链式法则」帮助理解 (不是证明)，
\begin{align
</div>
\frac{\partial f}{\partial z} = \frac{\partial f}{\partial x} \frac{\partial x}{\partial z}
+ \frac{\partial f}{\partial y} \frac{\partial y}{\partial z}
= \frac{1}{2} \frac{\partial f}{\partial x} - \frac{i}{2} \frac{\partial f}{\partial y}\
\frac{\partial f}{\partial \bar z} = \frac{\partial f}{\partial x} \frac{\partial x}{\partial \bar z}
+ \frac{\partial f}{\partial y} \frac{\partial y}{\partial \bar z}
= \frac{1}{2} \frac{\partial f}{\partial x} + \frac{i}{2} \frac{\partial f}{\partial y}
\end{align}
}
---
}
### Cauchy-Riemann 条件的复坐标表示
\item<+-> Cauchy-Riemann 条件可以重新组合
\begin{align}
& \ \frac{\partial u}{\partial x} - \frac{\partial v}{\partial y} = 0, \qquad
\frac{\partial u}{\partial y} + \frac{\partial v}{\partial x} = 0 \
\Rightarrow & \ \left(\frac{\partial u}{\partial x} - \frac{\partial v}{\partial y}\right)
+ i \left(\frac{\partial u}{\partial y} + \frac{\partial v}{\partial x}\right) = 0\
\Rightarrow & \ \frac{\partial (\green{u + i v})}{\partial x} + i \frac{\partial (\green{u + i v})}{\partial y} = 0 \
\Rightarrow & \ \partial_{\bar z} \green{f} = 0 \ .
\end{align}
\item<+-> CR 条件 $\Leftrightarrow$ $\partial_{\bar z} f = 0$
---
}
### 复导数
\item<+-> 实变函数的偏导数建基于自变量 $\Delta x, \Delta y$ 的 **<orange>独立性**
\begin{align}
\frac{\partial u}{\partial x} = \lim_{\Delta x \to 0} \frac{u(x + \Delta x, y) - u(x,y)}{\Delta x}
\end{align}
\item<+-> 特别地，
\begin{align}
\frac{\partial x}{\partial y} = \frac{\partial y}{\partial x} = 0, \qquad
\frac{\partial x}{\partial x} = \frac{\partial y}{\partial y} = 1 \ .
\end{align}

---

---
}
### 复导数
\item<+-> $z$ 与 $\bar z$ **<red>不独立**：$z \to z + \Delta \Rightarrow \bar z \to \bar z + \overline{\Delta z}$。
\item<+-> 但是，偏导算符呈现 **<orange>形式上独立性 (formal independence)**，
\begin{align}
\frac{\partial \bar z}{\partial z} = \frac{\partial z}{\partial \bar z} = 0, \qquad
\frac{\partial z}{\partial z} = \frac{\partial \bar z}{\partial \bar z} = 1\ .
\end{align}
<div class='proof'>
**证明**
直接计算。比如，结合 $z = x + iy$, $\bar z = x - i y$，
\begin{align
</div>
\frac{\partial \bar z}{\partial z}
= & \ \frac{1}{2} \left[\frac{\partial (x- i y)}{\partial x} - i \frac{\partial (x- i y)}{\partial y}\right]
= \frac{1}{2} \left[1 - i (-i)\right] = 0 \
\frac{\partial z}{\partial z}
= & \ \frac{1}{2} \left[\frac{\partial (x + i y)}{\partial x} - i \frac{\partial (x + i y)}{\partial y}\right]
= \frac{1}{2} \left[1 - i (+i)\right] = 1 \ .
\end{align}
}
---
}
### 复导数
\item $z$ 与 $\bar z$ **<red>不独立**：$z \to z + \Delta \Rightarrow \bar z \to \bar z + \overline{\Delta z}$。
\item 但是，偏导算符呈现 **<orange>形式上独立性 (formal independence)**，
\begin{align}
\frac{\partial \bar z}{\partial z} = \frac{\partial z}{\partial \bar z} = 0, \qquad
\frac{\partial z}{\partial z} = \frac{\partial \bar z}{\partial \bar z} = 1\ .
\end{align}
\item<+-> **<orange>形式上**，$\partial_z, \partial_{\bar z}$ 跟「偏导数」完全一样：接受它们作为偏导存在，$z, \bar z$ **<orange>形式上独立**
\item<+-> 通常：$f$ 满足 CR 条件/解析性/全纯性 $\Leftrightarrow$ $f$ **<red>不含** $\bar z$ $\Leftrightarrow$ $\partial_{\bar z} f = 0$
<div class='proof comment'>
**奇点**
在 **<orange>奇点** 处，$f$ 只依赖 $z$ $\not\Rightarrow$ $\partial_{\bar z
</div>
f = 0$。
}
---
}
### 复导数
\item<+-> 若 $f$ 解析/全纯，则其 **<orange>复共轭** $\overline{f(z)}$ 满足
\begin{align}
\partial_z \overline{f(z)} = 0\ .
\end{align}
这样的函数称为 **<green>反全纯 (anti-holomorphic) 函数**。
\item<+-> 一般的复变函数既不是全纯也不是反全纯函数。可以用 $f(z, \bar z)$ 来标明/强调。
---
}
### 共轭调和函数
\item<+-> 区域 $D$ 上解析的函数 $f = u + i v$ 的实部和虚部满足 CR 条件
\begin{align}
\frac{\partial u}{\partial x} = \frac{\partial v}{\partial y}, \qquad
\frac{\partial u}{\partial y} = - \frac{\partial v}{\partial x} \ .
\end{align}
\item<+-> 对这些方程两边作 $\partial_x$ 和 $\partial_y$
\begin{align}
\partial_x: \frac{\partial^2 u}{\partial x^2} = & \ \frac{\partial^2 v}{\partial x\partial y},
& \frac{\partial^2 u}{\partial x\partial y} = & \ - \frac{\partial^2 v}{\partial x^2} \
\partial_y: \frac{\partial^2 u}{\partial y\partial x} = & \ \frac{\partial^2 v}{\partial y^2},
&\frac{\partial^2 u}{\partial y^2} = & \ - \frac{\partial^2 v}{\partial y\partial x} \ .
\end{align}
\item<+-> 稍作比较可得两条 **<green>调和 (harmonic) 方程**
\begin{align}
\frac{\partial^2 u}{\partial x^2} + \frac{\partial^2 u}{\partial y^2} = 0, \qquad
\frac{\partial^2 v}{\partial x^2} + \frac{\partial^2 v}{\partial y^2} = 0
\end{align}
---
}
### 共轭调和函数
\item<+-> 解析函数的实部和虚部均为 **<green>调和函数**，而且二者由 CR 条件相互锁定，形成一对 **<green>共轭调和函数**。
\item<+-> 从一个调和函数 $u$ (或 $v$) 出发，可以 **<orange>通过 CR 条件** 解出与之共轭的调和同伴 $v$ (或 $u$)，并由此获得 **<orange>解析函数**。
---
}
### 共轭调和函数：例子
\item<+-> 设 $u(x,y) = x^2 - y^2$。若 $v(x,y)$ 是是与之共轭的调和函数，则必然有
\begin{align}
dv
= & \ \frac{\partial v}{\partial x}dx + \frac{\partial v}{\partial y} dy
= - \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy \
= & \ + 2y dx + 2x dy = d(2 xy) \ .
\end{align}
\item<+-> 于是 $v = 2 xy + C$，而 $C$ 是 **<green>任意实常数**。
\item<+-> 从而 $f(z) = x^2 - y^2 + 2i xy + Ci = (x + i y)^2 + C i $ 是 **<orange>解析函数**：
\begin{align}
f = z^2 + C \orange{i} \ .
\end{align}
---
}
### 共轭调和函数
\item<+-> 区域 $D$ 中共轭调和函数的 **<orange>线积分** 求解法：
\begin{align}
& \ dv
= \frac{\partial v}{\partial x}dx + \frac{\partial v}{\partial y} dy
= - \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy \
\Rightarrow & \ v = \int_{(x_0, y_0)}^{(x,y)} dv + C
= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
\end{align}
<div class='proof comment'>
**路径无关性**
上述积分只指定了初始点 $(x_0, y_0)$ 和终末点 $(x, y)$。
</div>
---
}
### 共轭调和函数
\item<+-> 区域 $D$ 中共轭调和函数的 **<orange>线积分** 求解法：
\begin{align}
v = \int_{(x_0, y_0)}^{(x,y)} dv + C
= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
\end{align}
<div class='proof comment'>
**路径无关性**
**<red>具体路径怎么选？**
\begin{figure
</div>
\includegraphics{image/three-paths.pdf}
</center>}
---
}
### 共轭调和函数
\item<+-> 区域 $D$ 中共轭调和函数的线积分求解法：
\begin{align}
v = \int_{(x_0, y_0)}^{(x,y)} dv + C
= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
\end{align}
<div class='proof comment'>
**路径几乎无关性**
绿色积分 $-$ 紫色积分 $=$ 闭合路径 $C$ 积分：
\begin{align
</div>
& \ \oint_C \left[\orange{- \frac{\partial u}{\partial y}}dx + \highlight{orange}{\frac{\partial u}{\partial x}}dy\right] =
\oint [\orange{P} dx + \highlight{orange}{Q} dy] \
\eqtext{Green's} & \ \int \left[
\frac{\partial}{\partial x} Q - \frac{\partial}{\partial y}P
\right]dx dy
= \int \left[
\frac{\partial^2 u}{\partial x^2} +
\frac{\partial^2 u}{\partial y^2}
\right]dx dy = 0 \ .
\end{align}
}
---
}
### 共轭调和函数
\item<+-> 区域 $D$ 中共轭调和函数的线积分求解法：
\begin{align}
v = \int_{(x_0, y_0)}^{(x,y)} dv + C
= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
\end{align}
<div class='proof comment'>
**路径几乎无关性**
蓝色积分 $-$ 绿色积分 $=$ 与 $(x, y), (x_0, y_0)$ 无关的常数 $C$：不要紧。
\vspace{1em
</div>
结论：随便选一条连接 $(x_0, y_0)$ 和 $(x,y)$ 的路径，只要 **<orange>完全在解析区内**。
}
---
}
### 共轭调和函数
\item<+-> 调和方程
\begin{align}
\frac{\partial^2 u}{\partial x^2} + \frac{\partial^2 u}{\partial y^2}
= \left[\frac{\partial^2}{\partial x^2} + \frac{\partial^2}{\partial y^2}\right] u = 0 \ .
\end{align}
\item<+-> **<orange>定理**：当 $u$ 足够光滑，
\begin{align}
\partial_z \partial_{\bar z} u = \frac{1}{4} \left[\frac{\partial^2}{\partial x^2} + \frac{\partial^2}{\partial y^2}\right]u \ .
\end{align}
<div class='proof'>
**证明**
直接利用复导数的定义，以及 $\partial_x \partial_y = \partial_y \partial_x$。
</div>
---
}
### 共轭调和函数
\item<+-> $\partial_z \partial_{\bar z} u = 0$ 的 **<orange>实** 解：
\begin{align}
u = \frac{1}{2} \left(f(z) + \overline{f(z)}\right) \ .
\end{align}
<div class='proof'>
**解**
显然
\begin{equation
</div>
\partial_z [\partial_{\bar z} f(z)] = 0 , \qquad
\partial_z \partial_{\bar z} \overline{f(z)} = \partial_{\bar z}[\partial_z \overline{f(z)}] = 0 \ .
\end{equation}
}
\item<+-> 与 $u$ 共轭的调和函数：$v = \frac{1}{2i} (f(z) - \overline{f(z)}) + C$.
<div class='proof comment'>
**重新组合**
把 $u$ 和 $v$ 重新组合
\begin{equation
</div>
u + i v
= \frac{1}{2}(f(z) + \overline{f(z)})
+ \frac{1}{2i}i (f(z) - \overline{f(z)})
= f(z) \ .
\end{equation}
}
---
}
### 共轭调和函数与静电场
\item<+-> 解析函数 $f$ 或者共轭调和函数 $(u, v)$ 可以用于描述 **<orange>静电场**
\item<+-> $u, v$ 的 **<orange>梯度**
\begin{align}
\nabla u = \left(\frac{\partial u}{\partial x}, \frac{\partial u}{\partial y}\right), \qquad
\nabla v = \left(\frac{\partial v}{\partial x}, \frac{\partial v}{\partial y}\right) \ .
\end{align}
\item<+-> $\nabla u \perp$ $u$ 等值线, $\nabla v \perp$ $v$ 等值线。
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.5]
%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300
%Shape: Polygon Curved [id:ds3736050678958065]
\draw   (127,103) .. controls (158.24,65.4) and (235.24,53.4) .. (235.24,129.4) .. controls (235.24,205.4) and (289.24,178.4) .. (309.24,208.4) .. controls (329.24,238.4) and (147.24,272.4) .. (127.24,242.4) .. controls (107.24,212.4) and (95.76,140.6) .. (127,103) -- cycle ;
%Shape: Polygon Curved [id:ds47258583745728866]
\draw   (131.24,130.4) .. controls (138.24,92.4) and (216.24,72.4) .. (216.24,148.4) .. controls (216.24,224.4) and (252.24,181.4) .. (272.24,211.4) .. controls (292.24,241.4) and (151.24,238.4) .. (138.24,208.4) .. controls (125.24,178.4) and (124.24,168.4) .. (131.24,130.4) -- cycle ;
%Shape: Polygon Curved [id:ds7798684470456769]
\draw   (145.24,155.4) .. controls (152.24,117.4) and (197.24,136.4) .. (198.24,163.4) .. controls (199.24,190.4) and (205.24,187.4) .. (228.24,212.4) .. controls (251.24,237.4) and (175.24,212.4) .. (167.24,207.4) .. controls (159.24,202.4) and (138.24,193.4) .. (145.24,155.4) -- cycle ;
%Straight Lines [id:da6892931376631548]
\draw    (108,181) -- (50.38,183.38) ;
\draw [shift={(48.38,183.47)}, rotate = 357.63] [color={rgb, 255:red, 0; green, 0; blue, 0 }  ][line width=0.75]    (10.93,-3.29) .. controls (6.95,-1.4) and (3.31,-0.3) .. (0,0) .. controls (3.31,0.3) and (6.95,1.4) .. (10.93,3.29)   ;
%Straight Lines [id:da6347469313644816]
\draw    (172,74.67) -- (161.52,31.74) ;
\draw [shift={(161.05,29.8)}, rotate = 76.29] [color={rgb, 255:red, 0; green, 0; blue, 0 }  ][line width=0.75]    (10.93,-3.29) .. controls (6.95,-1.4) and (3.31,-0.3) .. (0,0) .. controls (3.31,0.3) and (6.95,1.4) .. (10.93,3.29)   ;
%Straight Lines [id:da3694999736877975]
\draw    (309.24,208.4) -- (351.6,179.72) ;
\draw [shift={(353.25,178.6)}, rotate = 145.9] [color={rgb, 255:red, 0; green, 0; blue, 0 }  ][line width=0.75]    (10.93,-3.29) .. controls (6.95,-1.4) and (3.31,-0.3) .. (0,0) .. controls (3.31,0.3) and (6.95,1.4) .. (10.93,3.29)   ;
%Straight Lines [id:da5424257637203831]
\draw    (202,105.67) -- (227.01,74.17) ;
\draw [shift={(228.25,72.6)}, rotate = 128.45] [color={rgb, 255:red, 0; green, 0; blue, 0 }  ][line width=0.75]    (10.93,-3.29) .. controls (6.95,-1.4) and (3.31,-0.3) .. (0,0) .. controls (3.31,0.3) and (6.95,1.4) .. (10.93,3.29)   ;
% Text Node
\draw (50.38,186.47) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle \nabla u$};
% Text Node
\draw (150.38,105.47) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle u=u_{1}$};
% Text Node
\draw (247.38,150.47) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle u=u_{2}$};
% Text Node
\draw (257.38,75.47) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle u_{2}  >u_{1}$};
\end{tikzpicture}
</center>---
}
### 共轭调和函数与静电场
\item<+-> **<orange>定理**：共轭调和条件 $\Rightarrow$ 正交梯度 $\nabla u \cdot \nabla v = 0$
<div class='proof'>
**正交梯度**
直接计算
\begin{align
</div>
\nabla u \cdot \nabla v
= \frac{\partial u}{\partial x}\green{\frac{\partial v}{\partial x}}
+ \frac{\partial u}{\partial y} \highlight{orange}{\frac{\partial v}{\partial y}}
= - \frac{\partial u}{\partial x}\green{\frac{\partial u}{\partial y}}
+ \frac{\partial u}{\partial y}\highlight{orange}{\frac{\partial u}{\partial x}}
= 0 \ .
\end{align}
}
\item<+-> **<orange>推论**：等 $u$ 线 $\perp$ 等 $v$ 线。
---
}
### 共轭调和函数与静电场
\item<+-> 在无电荷分布二维静电场中，电势是平面空间的调和函数
<div class='proof'>
**二维电势**
二维无电荷麦克斯韦方程
\begin{align
</div>
\nabla \cdot \vec E(x,y) = 0\ .
\end{align}
然而电势与电场强度的关系是 $\vec E = - \nabla \varphi$，
\begin{align}
\Rightarrow \nabla \cdot \nabla \varphi
= \frac{\partial^2 \varphi}{\partial x^2} + \frac{\partial^2 \varphi}{\partial y^2} = 0 \ .
\end{align}
}

---

---
}
### 共轭调和函数与静电场
<div class='proof comment'>
**平面电场**
**<red>如何形成二维电场？**
\begin{center
</div>
![width:400px](image/MisakaMikoto.jpg)
</center>}
---
}
### 共轭调和函数与静电场
<div class='proof comment'>
**平面电场**
**<red>如何形成二维电场？**
\vspace{1em
</div>
可以使用无穷长直均匀带电直线
<center>
\includegraphics{image/infinitely-long-charged-line.pdf}
</center>}
---
}
### 初等函数
\item<+-> **<green>初等函数**：常数、幂函数、指数函数、对数函数、三角函数、反三角函数，及其有限次加减乘除、复合所构成的函数。
\item<+-> 导数公式与大一的内容一样
---
}
### 初等函数：整幂函数
\item<+-> **<orange>整幂** 复函数是全 $\mathbb{C}$ 解析的函数，定义为
\begin{align}
z^{n = 0, 1,2,3, \ldots} = \underbrace{z \cdot z \cdot \ldots \cdot z}_{n\text{ 个}} \ , \qquad
z^{n = -1, -2, \ldots} = \frac{1}{z^{n}} \ .
\end{align}
<div class='proof comment'>
**确定性**
给定一个 $z \in \mathbb{C
</div>
$，则 $z^n$ 是唯一 **<orange>确定** (单值) 的。
}
\item<+-> 满足整幂实函数的性质，
\begin{equation}
(z_1 z_2)^n = z_1^n z_2^n \ , \qquad (z_1 + z_2)^n = \sum_{k = 0}^{n}C_n^k z_1^k z_2^{n - k} \ .
\end{equation}
\item<+-> 若 $z = re^{i \theta}$，则 $z^n = r^n e^{i n \theta}$。
---
}
### 初等函数：多项式
\item<+-> 整幂函数通过复系数可以组合成 $n$-次 多项式，
\begin{align}
P(z) = \sum_{k = 0}^{n} a_k z^k \ , \qquad a_k \in \mathbb{C} \ , \quad a_n \ne 0\ .
\end{align}
\item<+-> 比如 $P(z) = z^2 - 1$，$P(z) = z^3 - 3 z^2 + 4 + 3 i$.
\item<+-> **<orange>代数基本定理**：任意 $n$-次多项式都有 $n$ 个复数根。
\begin{equation}
P(z) = a_n \prod_{k = 1}^{n} (z - z_k) \ .
\end{equation}
---
}
### 初等函数：多项式
\item<+-> 实数多项式 $P_\mathbb{R}(x) = \sum_{k = 0}^{n} r_k x^k $ 不一定有 $n$ 个 **<orange>实数根**。比如
\begin{align}
P(x) = x^2 + 1 \ .
\end{align}
\item<+-> 倘若允许 **<orange>复数根**，则 $x^2 + 1$ 恰好有两个根：
\visible<+->{$+i$, $-i$}
\visible<+->{$x^2 + 1 = (x - i)(x + i)$}
\item<+-> 又如 $x^3 - 1$ 只有一个实根：$+1$
\visible<+->{另外有两个复数根 $e^{\frac{2 \pi i}{3}}$, $e^{\frac{4 \pi i}{3}}$}
\visible<+->{$x^3 - 1 = (z - 1)(z - e^{\frac{2\pi i}{3}})(z - e^{\frac{4\pi i}{3}})$}
---
}
### 初等函数：次方根函数
\item<+-> 给定一个 $z \in \mathbb{C}$ 以及 $n \in \mathbb{N}_{\ge 1}$，若 $z_0 \in \mathbb{C}$ 满足 $z_0^n = z$，则称 $z_0$ 为 $z$ 的一个 **<green>$n$-次方根**，记作 $z^{\frac{1}{n}}$。
\item<+-> **<orange>定理**：若 $z = re^{i \theta}$，则 $z_0 = r^{\frac{1}{n}}e^{i \frac{\theta}{n}}$ 是 $z$ 的一个 $n$-次方根。
---
}
### 初等函数：次方根函数
\item<+-> **<orange>多值性定理**：给定 $z$ 之后，$n$-次方根 $z_0$ 有 **<orange>$n$ 个** 可能值。
<div class='proof'>
**多值性**
设 $z_0$ 是一个 $n$-次方根，即 $z_0^n = z$。则必然有
\begin{align
</div>
z_0, \qquad e^{\frac{2\pi i}{n}}z_0, \qquad e^{\frac{2\pi i}{n}\cdot 2} z_0, \qquad \cdots, \qquad e^{\frac{2\pi i}{n}(n - 1)}z_0
\end{align}
都是 $n$-次方根，因为 $(e^{\frac{2 \pi i}{n} k })^n = e^{2\pi i k} = 1$，$k = 0, 1, \dots, n - 1$。
}
---
}
### 初等函数：次方根函数
\item<+-> $n$-次方根的多值性来源于 **<orange>$z$ 的辐角** 的多值性
\begin{align}
& \ z = re^{i \theta} = r e^{i (\theta + 2\pi k)} \
\Rightarrow & \ z^{\frac{1}{n}} = r^{\frac{1}{n}} e^{i \frac{\theta}{n}},\qquad
\text{或者}, \qquad r^{\frac{1}{n}} e^{i (\frac{\theta}{n} + \frac{2\pi k}{n})} \ .
\end{align}
---
}
### 初等函数：次方根函数
\item<+-> $1$ 的 3 次方根
\begin{align}
& \ 1 = e^{0\pi i} = e^{2\pi i} = e^{4 \pi i}\
\Rightarrow & 
1^{\frac{1}{3}} = 1, e^{\frac{2\pi i}{3}}, e^{\frac{4\pi i}{3}} \ .
\end{align}
\item<+-> 多项式方程 $x^3 - 1 = 0$ 的解即为 $1$ 的 3 次方根。
\item<+-> $1$ 的 $n$ 次方根
\begin{align}
& \ 1 = e^{0\pi i} = e^{2\pi i} = \cdots = e^{2 \pi i (n - 1)}\
\Rightarrow & 
1^{\frac{1}{n}} = 1, e^{\frac{2\pi i}{n}}, \cdots, e^{\frac{2\pi i (n - 1)}{n}} \ .
\end{align}
---
}
### 初等函数：次方根函数
\item<+-> $1$ 的 $6$ 次方根图像分布
<center>
\includegraphics{image/roots-of-unity.pdf}
</center>---
}
### 初等函数：指数函数
\item<+-> **<green>复指数函数 $e^z$** 的定义是
\begin{align}
e^z = e^{x + i y} \coloneqq e^x e^{i y} = e^x (\cos y + i \sin y) \ .
\end{align}
\item<+-> 满足实指数函数的许多性质：
\begin{align}
\frac{d}{dz}e^z = e^z, \qquad e^{z_1} e^{z_2} = e^{z_1 + z_2} \ .
\end{align}
\item<+-> 全 $\mathbb{C}$ 解析的函数
---
}
### 初等函数：三角函数
\item<+-> **<green>复三角函数** 利用指数函数来定义
\begin{align}
\cos z \coloneqq \frac{1}{2}(e^{iz} + e^{-i z}), \qquad
\sin z \coloneqq \frac{1}{2i}(e^{i z} - e^{- i z}) \ .
\end{align}
\item<+-> 同样满足熟知的各种性质：和差化积、积化和差等
<div class='proof comment'>
**平方和**
计算 $\cos^2 z + \sin^2 z$，
\begin{align
</div>
= & \ \frac{1}{4}(e^{iz} + e^{- i z})^2 - \frac{1}{4}(e^{iz} - e^{- i z})^2\
= & \ \frac{1}{4}(e^{2iz} + e^{- 2i z} + 2) - \frac{1}{4}(e^{2iz} + e^{- 2i z} - 2) \
= & \ \frac{1}{4}2 - \frac{1}{4}(-2) = 1 \ .
\end{align}
}
---
}
### 初等函数：双曲函数
\item<+-> **<green>复双曲正弦余弦函数** 利用指数函数来定义
\begin{align}
\cosh z \coloneqq \frac{1}{2}(e^{z} + e^{- z}), \qquad
\sinh z \coloneqq \frac{1}{2}(e^{z} - e^{- z}) \ .
\end{align}
\item<+-> 同样满足熟知的各种性质：
\begin{equation}
\cosh^2 z - \sinh^2 z = 1, \quad \frac{d}{dz}\cosh z = \sinh z, \quad \frac{d}{dz}\sinh z = \cosh z \ . \nonumber
\end{equation}
\item<+-> 是全 $\mathbb{C}$ 解析的函数
\item<+-> 比值为双曲正切余切 $\tanh z$、$\coth z$
---
}
\item 双曲正弦余弦函数与三角函数的关系
\begin{equation}
\cosh z = \cos (iz), \qquad \sinh z = - i \sin (iz)
\end{equation}

---

---
}
### 初等函数：对数函数
\item<+-> **<green>定义**：若复数 $w$ 满足 $e^w = z$，则称 $w = \ln z$ 是 $z$ 的 **<green>对数**。
\item<+-> 作为 $z$ 的函数，对数函数 $\ln z$ 是 **<orange>多值** 的：若 $e^w = z$，则
\begin{align}
e^{w + 2\pi i n} = z, \qquad w, w + 2\pi n i  \text{ 都是 $z$ 的对数}\ , n \in \mathbb{Z} \ .
\end{align}
---
}
### 多值函数
\item<+-> 下面讨论具有一定解析性的函数的多值现象。
<div class='proof comment'>
**解析区域**
未说明解析区域的解析函数，就默认 **<orange>存在** 某个区域使得函数在上面解析。至于这个区域是什么，不需明说。
</div>
---
}
### 多值函数
\item<+-> 根式函数 $w = \sqrt{z}$ 是最简单的 **<orange>多值函数**
\begin{align}
z \to w = \sqrt{z}:\qquad w^2, (-w)^2 = z \ .
\end{align}
\item<+-> 也可以理解为来源于 $z$ 的辐角的多值性
\begin{align}
z = r e^{i \theta} = r e^{i (\theta + 2\pi)} \quad \to \quad
w = r^{\frac{1}{2}} e^{i \frac{\theta}{2}}, \quad  r^{\frac{1}{2}} e^{i \frac{\theta}{2}} \highlight{orange}{e^{\pi i}}
= \highlight{orange}{-} r^{\frac{1}{2}} e^{i \frac{\theta}{2}} \ .	 \nonumber
\end{align}
<div class='proof comment'>
**多值性**
只要 $z \ne 0$，则其平方根有 **<orange>两个** 不同值。当 $z = 0$，则平方根是 **<orange>唯一** 的。
</div>
---
}
<div class='proof comment'>
**多值现象**
\visible<+->{多值函数的函数值不能唯一确定，并不是说 **<red>完全无法得知函数值**，仅仅是有好几个确定的选项，不知道挑哪一个好而已。
</div>
\vspace{1em}
\visible<+->{比如，$f(z) = z^{1/2}$ 在 $z = 1$ 的函数值是 **<orange>非常明确** 的两个选项：$1, -1$；其它的数如 $250, 66666666, 2333$ 肯定 **<red>不是合理** 的 $f(1)$ 函数值。}
}
---
}
### 支点
\item<+-> **<green>定义**：对于 $w = \sqrt{z}$，当自变量 $z$ 绕原点周围 **<orange>任意小** 的闭曲线一圈，$w$ **<orange>都** 会发生 **<orange>多值现象** (具体体现为符号改变)。因此，称原点 $z = 0$ 为 $w = \sqrt{z}$ 的 **<green>支/分枝点 (branch point)**。
<center>
![width:560px](image/loki.png)
</center>---
}
### 支点
<center>
\includegraphics{image/branch-point.pdf}
</center>---
}
\href{./animation/SquareRoot.html}{![width:640px](SquareRoot.png)}
</center>---
}
### 支点
\item<+-> 再考虑根式函数 $w = \sqrt{\highlight{orange}{z - a}}$，$a \in \mathbb{C}$
\item<+-> 记 $\highlight{orange}{z - a} \coloneqq r e^{i \theta}$，则
\begin{align}
w = \sqrt{r}e^{i \frac{\theta}{2}}, \qquad - \sqrt{r}e^{i \frac{\theta}{2}} \ , \qquad \theta \text{ 是 $z - a$ 的辐角}\ .
\end{align}
\item<+-> 当 $z$ 绕 $a$ 任意小闭曲线一圈，$w$ 发生 **<orange>多值现象** (变号)。因此，称 $a$ 为函数 $w$ 的 **<green>支点**
\item<+-> 根式函数 $w = \sqrt{z - a}$ 在支点 $z = a$ 处 **<orange>有定义**：$w(z = a) = 0$。
---
}
\href{./animation/SquareRootZminusA.html}{ ![width:640px](SquareRootZminusA.png)}
</center>---
}
### 支点
<center>
\includegraphics{image/first-order-branch-point.pdf}
</center>---
}
### 支点
\item<+-> 当 $a \ne 0$，原点 $z = 0$ **<red>不再是** 支点：绕原点转小圈，$\sqrt{z -a}$ 不出现多值现象
<center>
![width:480px](image/non-branch-point.pdf)
</center>---
}
\href{./animation/SquareRootZminusA2.html}{
![width:640px](SquareRootZminusA2.png)
}
</center>---
}
### 支点
\item<+-> 对于 $w = \sqrt{z - a}$，$\infty$ 也是支点
<div class='proof comment'>
**绕 $\infty$**
「绕 $\infty$ 的 **<orange>任意小** 闭曲线」 $ \ = \ $ 「绕 $z = a$ **<orange>任意大** 的闭曲线」
</div>
---
}
### 支点
\item<+-> 对 $w = \sqrt{z - a}$，绕 $a$ 或者 $\infty$ 2 周之后 $w$ 变回原值。因此，定义 $a$, $\infty$ 为 $w = \sqrt{z - a}$ 的 **<green>一阶支点**。
\item<+-> 对 $w = (z - a)^{\frac{1}{3}}$，绕 $a$ 或者 $\infty$ 3 周之后 $w$ 变回原值。因此，定义 $a$, $\infty$ 为 $w = (z - a)^{\frac{1}{3}}$ 的 **<green>二阶支点**。
<div class='proof comment'>
**$2\pi/3$**
由于 $w$ 是开三次方，所以当 $z - a$ 的辐角增加 $2\pi, 4\pi$ 时，$w$ 感应到
\begin{align
</div>
w \to e^{\frac{i 2\pi}{3}} w \ ,\qquad
w \to e^{\frac{i 4\pi}{3}} w \ .
\end{align}
}
\item<+-> 对 $w(z)$ 的支点 $a$，若绕 $a$ 至少 $k + 1$ 周之后 $w$ 变回原值，则称 $a$ 为 $w(z)$ 的 **<green>$k$ 阶支点**。
---
}
### 支点：例
\item<+-> 考虑 $w(z) = \sqrt{(z - a)(z - b)}$。
\item<+-> 记 $z - a = r_a e^{i \theta_a}$，$z - b = r_b e^{i \theta_b}$
<center>
\includegraphics{image/double-branch-point.pdf}
</center>

---

---
}
### 支点：例
\item<+-> 当 $z$ 沿绕着 $b$ 无穷小的闭曲线一圈，出现多值现象：
\begin{align}
\left \{
\begin{array}{cc}
\theta_a \to \theta_a, \
\theta_b \to \theta_b + 2\pi
\end{array}
\right.
\Rightarrow \quad
w = \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}}
\to \highlight{orange}{-} \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}} \ .\nonumber
\end{align}
<center>
\includegraphics{image/multivaluedness.pdf}
</center>---
}
### 支点：例
\item<+-> 当 $z$ 沿绕着 $a$ 无穷小的闭曲线一圈，出现多值现象：
\begin{align}
\theta_a \to \theta_a + 2\pi, \quad \theta_b \to \theta_b  \ \Rightarrow
w = \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}}
\to \highlight{orange}{-} \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}} \ .\nonumber
\end{align}
<center>
</center>---
}
### 支点：例
\item<+-> 当 $z$ 沿绕着 $a, b$ 任意 **<orange>大** 的闭曲线一圈 **<red>没有** 出现多值现象：
\begin{align}
\theta_{a,b} \to \theta_{a,b} + 2\pi, \Rightarrow
w = \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}}
\to \orange{(-)^2} \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}} \ .\nonumber
\end{align}
<center>
\includegraphics{non-multivaluedness.pdf}
</center>---
}
### 支点：例
\item<+-> 总结：
(1) $b$ 是 $w = \sqrt{(z - a)(z - b)}$ 的一阶支点
(2) $a$ 是 $w = \sqrt{(z - a)(z - b)}$ 的一阶支点
(3) $\infty$ **<red>不是** 支点
---
}
### 支点：例
\item<+-> 考虑 $w(z) = \sqrt{(z - a)(z - b)(z - c)}$，$a,b,c$ 互异
\visible<+->{一阶支点：$\infty, a, b, c$}
\item<+-> 考虑 $w(z) = \sqrt{1 - \frac{1}{z}}$
\visible<+->{$w(z) = \sqrt{1 - \frac{1}{z}} = \sqrt{\frac{z - 1}{z}}$，一阶支点：$0, 1$。$\infty$ **<red>不是** 支点}
\item<+-> 考虑 $w(z) = (z^3 - 1)^{\frac{1}{3}}$
\visible<+->{$w(z) = [(z - 1)(z - e^{\frac{2\pi i }{3}})(z - e^{\frac{4\pi i }{3}})]^{\frac{1}{3}}$，二阶支点：$1$, $e^{\frac{2\pi i}{3}}$, $e^{\frac{4\pi i}{3}}$。$\infty$ **<red>不是** 支点}
---
}
### 支点：例
\item<+-> 考虑 $w = z^a (z - 1)^b$，$a, b \in \mathbb{Q}$
(1) 若 $a \not\in \mathbb{Z}$，则 $z = 0$ 是支点
(2) 若 $b \not\in \mathbb{Z}$，则 $z = 1$ 是支点
(3) 若 $a + b \not\in \mathbb{Z}$，则 $z = \infty$ 是支点
---
}
### 支点：例
\item<+-> 考虑 $w = \ln z$
\item<+-> 给定 $z$，所对应的对数 $w$ 不唯一：可以相差任意的 $2\pi i n$。
\item<+-> 支点：$z = 0, \infty$
\item<+-> 不管绕多少圈，都无法返回原值：**<green>超越 (transcendental) 支点**
\item<+-> $\ln z$ 在 $z = 0$ **<orange>无定义**：与根式函数 $w = \sqrt{z}$ 不一样
---
}
### 支点
\item<+-> 对于解析函数，支点可能是 **<orange>连续点**
\item<+-> 通常认为支点是 **<orange>奇点**：支点处 **<orange>导数** **<red>无法定义**。
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.8]
%uncomment if require: \path (0,228); %set diagram left start at 0, and has height of 228
%Straight Lines [id:da484114713151669]
\draw  [dash pattern={on 0.84pt off 2.51pt}]  (239.85,123.34) -- (132.92,123.34) ;
%Shape: Circle [id:dp7170235575117196]
\draw  [color={rgb, 255:red, 0; green, 0; blue, 0 }  ,draw opacity=1 ][fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (130.08,123.34) .. controls (130.08,121.77) and (131.35,120.5) .. (132.92,120.5) .. controls (134.49,120.5) and (135.77,121.77) .. (135.77,123.34) .. controls (135.77,124.92) and (134.49,126.19) .. (132.92,126.19) .. controls (131.35,126.19) and (130.08,124.92) .. (130.08,123.34) -- cycle ;
%Shape: Circle [id:dp35726699635873294]
\draw  [color={rgb, 255:red, 0; green, 0; blue, 0 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (205.68,112.14) .. controls (205.68,110.57) and (206.95,109.3) .. (208.52,109.3) .. controls (210.09,109.3) and (211.37,110.57) .. (211.37,112.14) .. controls (211.37,113.72) and (210.09,114.99) .. (208.52,114.99) .. controls (206.95,114.99) and (205.68,113.72) .. (205.68,112.14) -- cycle ;
%Shape: Circle [id:dp09122258961040264]
\draw  [color={rgb, 255:red, 0; green, 0; blue, 0 }  ,draw opacity=1 ][fill={rgb, 255:red, 144; green, 19; blue, 254 }  ,fill opacity=1 ] (206.48,133.74) .. controls (206.48,132.17) and (207.75,130.9) .. (209.32,130.9) .. controls (210.89,130.9) and (212.17,132.17) .. (212.17,133.74) .. controls (212.17,135.32) and (210.89,136.59) .. (209.32,136.59) .. controls (207.75,136.59) and (206.48,135.32) .. (206.48,133.74) -- cycle ;
%Straight Lines [id:da6229029204967647]
\draw [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ]   (208.52,112.14) -- (134.9,123.05) ;
\draw [shift={(132.92,123.34)}, rotate = 351.57] [fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ][line width=0.08]  [draw opacity=0] (12,-3) -- (0,0) -- (12,3) -- cycle    ;
%Straight Lines [id:da3940020916880167]
\draw [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ]   (209.32,133.74) -- (134.9,123.61) ;
\draw [shift={(132.92,123.34)}, rotate = 7.75] [fill={rgb, 255:red, 144; green, 19; blue, 254 }  ,fill opacity=1 ][line width=0.08]  [draw opacity=0] (12,-3) -- (0,0) -- (12,3) -- cycle    ;
%Shape: Arc [id:dp3727603469169103]
\draw  [draw opacity=0][fill={rgb, 255:red, 144; green, 19; blue, 254 }  ,fill opacity=0.05 ][dash pattern={on 4.5pt off 4.5pt}] (208.5,134.26) .. controls (203.35,171.21) and (171.63,199.65) .. (133.26,199.65) .. controls (91.31,199.65) and (57.3,165.64) .. (57.3,123.68) .. controls (57.3,81.73) and (91.31,47.72) .. (133.26,47.72) .. controls (171.29,47.72) and (202.8,75.66) .. (208.36,112.14) -- (133.26,123.68) -- cycle ; \draw  [dash pattern={on 4.5pt off 4.5pt}] (208.5,134.26) .. controls (203.35,171.21) and (171.63,199.65) .. (133.26,199.65) .. controls (91.31,199.65) and (57.3,165.64) .. (57.3,123.68) .. controls (57.3,81.73) and (91.31,47.72) .. (133.26,47.72) .. controls (171.29,47.72) and (202.8,75.66) .. (208.36,112.14) ;
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (60.37,121.27) -- (57.45,127.11) -- (54.53,121.27) -- (57.45,124.19) -- cycle ;
% Text Node
\draw (134.92,126.34) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle a$};
% Text Node
\draw (210.52,109.14) node [anchor=south west] [inner sep=0.75pt]  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,opacity=1 ] [align=left] {$\displaystyle a+\Delta z$};
% Text Node
\draw (211.32,136.74) node [anchor=north west][inner sep=0.75pt]  [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,opacity=1 ] [align=left] {$\displaystyle a+\Delta z'$};
% Text Node
\draw (272,65.53) node [anchor=north west][inner sep=0.75pt]   [align=left] {多值性：$\displaystyle f( a+\Delta z)$ 与 $\displaystyle f( a+\Delta z')$\\函数值相差甚远};
% Text Node
\draw (272,126.53) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle  \begin{array}{{>{\displaystyle}l}}
\lim _{\Delta z\rightarrow 0}\frac{f( a+\Delta z) -f( a)}{\Delta z}\
\ \ \ \ \ \ \ \ \ \ \ \ \neq \lim _{\Delta z'\rightarrow 0}\frac{f( a+\Delta z') -f( a)}{\Delta z'}
\end{array}$};
\end{tikzpicture}
</center>---
}
### 割线
\item<+-> 为了 **<orange>禁止** 多值现象，可以从支点出发画一条曲线把 $\mathbb{C}$ 割破，称为 **<green>割线**，\visible<+->{并 **<orange>明令禁止** $z$ 穿过割线。}\visible<+->{与此同时，**<orange>人为** **<orange>选** 定割线外 **<orange>一点** 的函数值作为 **<orange>基准**。}
\visible<+->{$\Rightarrow$ **<orange>唯一确定** 整个连通解析区的函数值，完全 **<orange>杜绝** 多值现象}
<div class='proof comment'>
**多个支点**
\visible<+->{当函数有 **<orange>多个支点** 的时候，需要 **<orange>每个** 支点都连上割线。若有支点无割线，则出现 **<orange>多值漏洞**
</div>
}
---
}
### 割线：例
\item<+-> 考虑 $w = \sqrt{z - a}$\visible<+->{，支点是 $a, \infty$}
\visible<+->{<center>
\includegraphics{branch-cut.pdf}
</center>}
---
}
### 割线：例
\item<+-> 考虑 $w = \sqrt{(z - a)(z - b)}$，\visible<+->{支点是 $a, b$}
\visible<+->{
<center>
\includegraphics{double-branch-cut.pdf}
</center>}
---
}
### 割线
<div class='proof comment'>
**非局域算符**
在未来的物理学习中，会碰到 **<orange>准局域算符** 和连接准局域算符的 **<orange>拓扑线算符**：这些东西跟跟支点、割线非常相似。
\vspace{1em
</div>
关键词：狄拉克磁单极与狄拉克弦，伊辛模型、disorder 算符和 non-invertible defect line
}
---
}
### 割线：例
\item<+-> 割线不唯一
\item<+-> 再次考虑 $w = \sqrt{(z - a)(z - b)}$，$a \ne b$，支点为 $a,b$
<center>
\includegraphics{branch-cut-not-unique.pdf}
</center>---
}
### 割线：例
\item<+-> 割线不唯一
\item<+-> 考虑 $w = {(z - a)^{\frac{1}{2}}(z - b)^{\frac{1}{3}}}$，$a \ne b$，支点为 $a,b, \infty$
<center>
\includegraphics{branch-cut-not-unique-2.pdf}
</center>---
}
### 割线与基准值
\item<+->
<div class='proof comment'>
**选定基准值**
在设置割线时，需要人为 **<orange>选** 定割线外某点的函数值作为基准。一旦选好了，割线外的所有点函数值均可以通过 **<orange>连续性** **<orange>唯一** 确定。
</div>

---

---
}
### 割线与基准值：例
\item<+-> 考虑 $f(z) = \sqrt{z}$。支点 $0, \infty$。选割线为 $(-\infty, 0]$
\item<+-> 选择 $f(z = 1) = +1$
<center>
\includegraphics{branch-cut-and-reference-value.pdf}
</center>\item<+-> 得到 $f(-1 + i \epsilon) = + i$，$f(- 1 - i \epsilon) = - i$，$\epsilon \to 0+$
---
}
### 割线：例
\item<+-> 更一般地，对 $x < 0$，割线的上岸和下岸的值已被确定，
\begin{align}
f(x + i \epsilon) = i |x|^{\frac{1}{2}}, \qquad
f(x - i \epsilon) = - i |x|^{\frac{1}{2}} \ .
\end{align}
<div class='proof comment'>
**割线**
割线上的点已经被舍弃，$f(x < 0)$ **<orange>无定义**。
</div>
---
}
### 其它常见多值函数
\item<+-> 多值函数无处不在，除了根式函数，还有
\item<+-> 一般幂次函数 $z^\alpha$，$\alpha \in \mathbb{C}$
\item<+-> 对数函数：$\ln (z - a)$
\item<+-> 反三角函数 $\arcsin z$, $\arccos$, $\arctan$ 等
\item<+-> 单值函数、多值函数的各种复合，如
\begin{equation}
\left(\frac{z - a}{z - b}\right)^\alpha, \qquad
\ln \left(z^\alpha + (1 - z^2)^\beta\right) \ , \qquad \cdots \ .
\end{equation}
---
}
### 其他空间上的复分析
\item<+-> 除了割线，还有别的方法治理多值性问题：多叶黎曼面 (Riemann surface)
\item<+-> 以 $f(z) = \sqrt{z}$ 为例：原点是一个 **<orange>支点**，绕原点一圈会有多值现象
\begin{equation}
f(e^{2\pi i}z) = - f(z) \ .
\end{equation}
\item<+-> 杜绝多值性的老方法：画割线，禁止穿越
\item<+-> 杜绝多值性的新方法：**<orange>扩展定义域**，把 $e^{2\pi i}z$ 与 $z$ 看成两个 **<orange>不同的点**，但同时尊重 **<orange>连续性**
---
}
\item<+-> 先只关注单位圆，把单位圆所有点复制一遍
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.6]
%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300
%Shape: Circle [id:dp7727328764543693]
\draw  [dash pattern={on 4.5pt off 4.5pt}] (56,145.88) .. controls (56,112.81) and (82.81,86) .. (115.88,86) .. controls (148.95,86) and (175.76,112.81) .. (175.76,145.88) .. controls (175.76,178.95) and (148.95,205.76) .. (115.88,205.76) .. controls (82.81,205.76) and (56,178.95) .. (56,145.88) -- cycle ;
%Shape: Circle [id:dp02247454581082553]
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (111.88,145.88) .. controls (111.88,143.67) and (113.67,141.88) .. (115.88,141.88) .. controls (118.09,141.88) and (119.88,143.67) .. (119.88,145.88) .. controls (119.88,148.09) and (118.09,149.88) .. (115.88,149.88) .. controls (113.67,149.88) and (111.88,148.09) .. (111.88,145.88) -- cycle ;
%Shape: Circle [id:dp4751283197599847]
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (171.76,145.88) .. controls (171.76,143.67) and (173.55,141.88) .. (175.76,141.88) .. controls (177.97,141.88) and (179.76,143.67) .. (179.76,145.88) .. controls (179.76,148.09) and (177.97,149.88) .. (175.76,149.88) .. controls (173.55,149.88) and (171.76,148.09) .. (171.76,145.88) -- cycle ;
%Shape: Arc [id:dp5913150652123564]
\draw  [draw opacity=0] (159.28,115.28) .. controls (161.73,118.76) and (163.8,122.59) .. (165.4,126.74) .. controls (167.7,132.69) and (168.86,138.8) .. (168.98,144.83) -- (115.88,145.88) -- cycle ; \draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ] (159.28,115.28) .. controls (161.73,118.76) and (163.8,122.59) .. (165.4,126.74) .. controls (167.7,132.69) and (168.86,138.8) .. (168.98,144.83) ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (159.84,123.42) -- (159.23,114.5) -- (166.74,119.36) -- (161.26,117.95) -- cycle ;
%Shape: Circle [id:dp28687598323513197]
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=0.07 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=0.5 ] (167.09,170.21) .. controls (167.09,168) and (168.88,166.21) .. (171.09,166.21) .. controls (173.3,166.21) and (175.09,168) .. (175.09,170.21) .. controls (175.09,172.42) and (173.3,174.21) .. (171.09,174.21) .. controls (168.88,174.21) and (167.09,172.42) .. (167.09,170.21) -- cycle ;
%Shape: Arc [id:dp5066432149780815]
\draw  [draw opacity=0] (168.61,152.15) .. controls (167.69,159.88) and (165.07,167.3) .. (161,173.87) -- (115.88,145.88) -- cycle ; \draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=0.5 ] (168.61,152.15) .. controls (167.69,159.88) and (165.07,167.3) .. (161,173.87) ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=0.09 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=0.5 ] (163.53,156.08) -- (169.23,149.18) -- (171.32,157.88) -- (168.33,153.08) -- cycle ;
%Shape: Ellipse [id:dp24814240841205115]
\draw   (326.95,126.67) .. controls (326.95,115.62) and (357.47,106.67) .. (395.12,106.67) .. controls (432.77,106.67) and (463.29,115.62) .. (463.29,126.67) .. controls (463.29,137.71) and (432.77,146.67) .. (395.12,146.67) .. controls (357.47,146.67) and (326.95,137.71) .. (326.95,126.67) -- cycle ;
%Shape: Ellipse [id:dp7802088486615635]
\draw   (326.95,176.67) .. controls (326.95,165.62) and (357.47,156.67) .. (395.12,156.67) .. controls (432.77,156.67) and (463.29,165.62) .. (463.29,176.67) .. controls (463.29,187.71) and (432.77,196.67) .. (395.12,196.67) .. controls (357.47,196.67) and (326.95,187.71) .. (326.95,176.67) -- cycle ;
%Shape: Circle [id:dp8196552141410924]
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (391.12,126.67) .. controls (391.12,124.46) and (392.91,122.67) .. (395.12,122.67) .. controls (397.33,122.67) and (399.12,124.46) .. (399.12,126.67) .. controls (399.12,128.88) and (397.33,130.67) .. (395.12,130.67) .. controls (392.91,130.67) and (391.12,128.88) .. (391.12,126.67) -- cycle ;
%Shape: Circle [id:dp6696435217641126]
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (391.12,176.67) .. controls (391.12,174.46) and (392.91,172.67) .. (395.12,172.67) .. controls (397.33,172.67) and (399.12,174.46) .. (399.12,176.67) .. controls (399.12,178.88) and (397.33,180.67) .. (395.12,180.67) .. controls (392.91,180.67) and (391.12,178.88) .. (391.12,176.67) -- cycle ;
%Shape: Circle [id:dp609227020765152]
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (459.29,126.67) .. controls (459.29,124.46) and (461.08,122.67) .. (463.29,122.67) .. controls (465.5,122.67) and (467.29,124.46) .. (467.29,126.67) .. controls (467.29,128.88) and (465.5,130.67) .. (463.29,130.67) .. controls (461.08,130.67) and (459.29,128.88) .. (459.29,126.67) -- cycle ;
%Shape: Circle [id:dp765601205345209]
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (459.29,176.67) .. controls (459.29,174.46) and (461.08,172.67) .. (463.29,172.67) .. controls (465.5,172.67) and (467.29,174.46) .. (467.29,176.67) .. controls (467.29,178.88) and (465.5,180.67) .. (463.29,180.67) .. controls (461.08,180.67) and (459.29,178.88) .. (459.29,176.67) -- cycle ;
% Text Node
\draw (184.76,139.28) node [anchor=north west][inner sep=0.75pt]  [color={rgb, 255:red, 139; green, 87; blue, 42 }  ,opacity=1 ]  {$选择f( 1) =1$};
% Text Node
\draw (173.09,173.61) node [anchor=north west][inner sep=0.75pt]  [color={rgb, 255:red, 139; green, 87; blue, 42 }  ,opacity=1 ]  {$f\left( e^{2\pi i} 1\right) =-1$};
% Text Node
\draw (115.88,153.28) node [anchor=north] [inner sep=0.75pt]    {$f( 0) =0$};
% Text Node
\draw (460,130.07) node [anchor=north west][inner sep=0.75pt]    {$f\left( e^{2\pi i} 1\right) =-1$};
% Text Node
\draw (465.29,180.07) node [anchor=north west][inner sep=0.75pt]    {$f( 1) =1$};
\end{tikzpicture}
<center>
![width:640px](image/volcano.png)
</center></center>---
}
\item<+-> 两个原点合同，因为 $f(z)$ 在原点没有多值性
<div class='proof comment'>
**极简主义**
数学家崇尚 **<orange>极简主义**，不引入非必要的东西。$f(z)$ 在原点处本身 $f(0) = 0$，**<red>没有多值** 现象，**<red>不需要** 复制多一份原点。
</div>
---
}
\item<+-> 为了保持连续性，把两个单位圆连接起来
<center>
![width:560px](image/connect.jpg)
</center>---
}
\href{./animation/DoubleCircle.html}{![width:640px](DoubleCircle.png)}
</center>---
}
\item<+-> 为了保持连续性，把两个单位圆连接起来
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1]
%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300
%Shape: Circle [id:dp9081192641928111]
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (176.12,94.67) .. controls (176.12,92.46) and (177.91,90.67) .. (180.12,90.67) .. controls (182.33,90.67) and (184.12,92.46) .. (184.12,94.67) .. controls (184.12,96.88) and (182.33,98.67) .. (180.12,98.67) .. controls (177.91,98.67) and (176.12,96.88) .. (176.12,94.67) -- cycle ;
%Shape: Circle [id:dp8343641741298735]
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (176.12,144.67) .. controls (176.12,142.46) and (177.91,140.67) .. (180.12,140.67) .. controls (182.33,140.67) and (184.12,142.46) .. (184.12,144.67) .. controls (184.12,146.88) and (182.33,148.67) .. (180.12,148.67) .. controls (177.91,148.67) and (176.12,146.88) .. (176.12,144.67) -- cycle ;
%Shape: Circle [id:dp519628372458337]
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (244.29,94.67) .. controls (244.29,92.46) and (246.08,90.67) .. (248.29,90.67) .. controls (250.5,90.67) and (252.29,92.46) .. (252.29,94.67) .. controls (252.29,96.88) and (250.5,98.67) .. (248.29,98.67) .. controls (246.08,98.67) and (244.29,96.88) .. (244.29,94.67) -- cycle ;
%Shape: Circle [id:dp3109743739525104]
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (244.29,144.67) .. controls (244.29,142.46) and (246.08,140.67) .. (248.29,140.67) .. controls (250.5,140.67) and (252.29,142.46) .. (252.29,144.67) .. controls (252.29,146.88) and (250.5,148.67) .. (248.29,148.67) .. controls (246.08,148.67) and (244.29,146.88) .. (244.29,144.67) -- cycle ;
%Straight Lines [id:da018876663471852684]
\draw    (180.12,94.67) -- (180.12,144.67) ;
%Straight Lines [id:da7037334539323612]
\draw    (180.12,144.67) -- (180.12,169.87) ;
%Straight Lines [id:da4157725534620731]
\draw    (180.12,64.27) -- (180.12,94.67) ;
%Curve Lines [id:da5395815094655585]
\draw [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ]   (248.29,144.67) .. controls (250.13,118.52) and (112.13,119.32) .. (111.95,144.67) .. controls (111.76,170.01) and (221.45,168.98) .. (235.16,153.84) .. controls (248.88,138.7) and (248.72,120.13) .. (248.29,94.67) .. controls (247.85,69.21) and (112.35,69.46) .. (111.95,94.67) .. controls (111.54,119.88) and (221.21,116.54) .. (234.88,106.7) .. controls (248.55,96.85) and (238.63,149.05) .. (240.33,153.83) .. controls (242.04,158.61) and (244.33,155.12) .. (246.48,149.83) ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (200.29,130.02) -- (192.63,125.41) -- (200.91,122.04) -- (196.61,125.72) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (243.83,119.94) -- (248.06,112.06) -- (251.83,120.17) -- (247.95,116.06) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (210.96,81.02) -- (203.29,76.41) -- (211.58,73.04) -- (207.28,76.72) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (162.52,109.5) -- (170.27,113.96) -- (162.05,117.48) -- (166.28,113.72) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (158.85,160.16) -- (166.61,164.62) -- (158.39,168.15) -- (162.61,164.39) -- cycle ;
\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (245.97,121.83) -- (241.38,129.51) -- (237.99,121.24) -- (241.68,125.52) -- cycle ;
% Text Node
\draw (250.29,98.07) node [anchor=north west][inner sep=0.75pt]    {$f\left( e^{2\pi i} 1\right) =-1$};
% Text Node
\draw (250.29,148.07) node [anchor=north west][inner sep=0.75pt]    {$f( 1) =1$};
% Text Node
\draw (157.63,186.4) node [anchor=north west][inner sep=0.75pt]   [align=left] {第二步};
\end{tikzpicture}
![width:320px](image/two-sheeted.png)
</center><div class='proof comment'>
**一阶支点**
注意原点是一阶支点，**<orange>转两圈**，多值性消失。
</div>
---
}
\item<+-> $\sqrt{z}$ 对应的 Riemann surface
<center>
![width:320px](image/sqrt.svg.png)
双叶</center>---
}
\item<+-> $\sqrt{z}$ 对应的 Riemann surface
<div class='proof comment'>
**虚假的交汇**
双叶黎曼面实际 **<red>并没自相交**，图中的交线来源于三维绘图空间的束缚。可以在高维空间中绘制不自交的双叶黎曼面。
</div>
---
}
\item<+-> $z^{1/3}$ 对应的 Riemann surface
<center>
![width:320px](image/cube_root.svg.png)
三叶</center>---
}
\item<+-> $z^{1/4}$ 对应的 Riemann surface
<center>
![width:320px](image/4th_root.svg.png)
四叶</center>---
}
\item<+-> $\ln z$ 对应的 Riemann surface
<center>
![width:240px](image/log.svg.png)
无限城</center>---
}
### 其他空间上的复分析
\item<+-> 除了 $\mathbb{C}$，还可以在其他空间上研究复变函数和微积分
\item<+-> 最简单的例子：$\mathbb{C} \cup \{\infty\} = \mathbb{C}P^1$ 上的复变函数实际上就是我们课程的内容
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.7]
%uncomment if require: \path (0,228); %set diagram left start at 0, and has height of 228
%Flowchart: Connector [id:dp8860671924548542]
\draw  [fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.12 ][line width=1.5]  (130,116.36) .. controls (130,65.35) and (171.35,24) .. (222.36,24) .. controls (273.38,24) and (314.73,65.35) .. (314.73,116.36) .. controls (314.73,167.38) and (273.38,208.73) .. (222.36,208.73) .. controls (171.35,208.73) and (130,167.38) .. (130,116.36) -- cycle ;
%Shape: Arc [id:dp5928904764363037]
\draw  [draw opacity=0][dash pattern={on 4.5pt off 4.5pt}] (130,116.36) .. controls (130,99.8) and (171.24,86.36) .. (222.11,86.36) .. controls (272.99,86.36) and (314.23,99.8) .. (314.23,116.36) .. controls (314.23,116.36) and (314.23,116.36) .. (314.23,116.36) -- (222.11,116.36) -- cycle ; \draw  [dash pattern={on 4.5pt off 4.5pt}] (130,116.36) .. controls (130,99.8) and (171.24,86.36) .. (222.11,86.36) .. controls (272.99,86.36) and (314.23,99.8) .. (314.23,116.36) .. controls (314.23,116.36) and (314.23,116.36) .. (314.23,116.36) ;
%Shape: Arc [id:dp669805676379083]
\draw  [draw opacity=0] (314.73,116.36) .. controls (314.73,132.93) and (273.49,146.36) .. (222.61,146.36) .. controls (171.74,146.36) and (130.5,132.93) .. (130.5,116.36) .. controls (130.5,116.36) and (130.5,116.36) .. (130.5,116.36) -- (222.61,116.36) -- cycle ; \draw   (314.73,116.36) .. controls (314.73,132.93) and (273.49,146.36) .. (222.61,146.36) .. controls (171.74,146.36) and (130.5,132.93) .. (130.5,116.36) .. controls (130.5,116.36) and (130.5,116.36) .. (130.5,116.36) ;
\end{tikzpicture}
</center>

---

---
}
### 其他空间上的复分析
\item<+-> 例子：二维环面 $T^2$
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.7]
%uncomment if require: \path (0,241); %set diagram left start at 0, and has height of 241
%Shape: Ellipse [id:dp9448478903864244]
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=0.01 ][line width=1.5]  (101,135.24) .. controls (101,94.24) and (170.88,61) .. (257.09,61) .. controls (343.3,61) and (413.18,94.24) .. (413.18,135.24) .. controls (413.18,176.24) and (343.3,209.48) .. (257.09,209.48) .. controls (170.88,209.48) and (101,176.24) .. (101,135.24) -- cycle ;
%Shape: Arc [id:dp25432641807910294]
\draw  [draw opacity=0][line width=1.5]  (360.13,119.29) .. controls (360.16,119.61) and (360.18,119.92) .. (360.18,120.24) .. controls (360.18,136.94) and (314.25,150.48) .. (257.59,150.48) .. controls (202.01,150.48) and (156.75,137.45) .. (155.05,121.19) -- (257.59,120.24) -- cycle ; \draw  [line width=1.5]  (360.13,119.29) .. controls (360.16,119.61) and (360.18,119.92) .. (360.18,120.24) .. controls (360.18,136.94) and (314.25,150.48) .. (257.59,150.48) .. controls (202.01,150.48) and (156.75,137.45) .. (155.05,121.19) ;
%Shape: Arc [id:dp6163497515775678]
\draw  [draw opacity=0][line width=1.5]  (175.03,139) .. controls (175.01,138.75) and (175,138.49) .. (175,138.24) .. controls (175,122.64) and (211.75,110) .. (257.09,110) .. controls (301.69,110) and (337.98,122.24) .. (339.15,137.48) -- (257.09,138.24) -- cycle ; \draw  [line width=1.5]  (175.03,139) .. controls (175.01,138.75) and (175,138.49) .. (175,138.24) .. controls (175,122.64) and (211.75,110) .. (257.09,110) .. controls (301.69,110) and (337.98,122.24) .. (339.15,137.48) ;
%Shape: Circle [id:dp6696950641438213]
\draw  [color={rgb, 255:red, 0; green, 0; blue, 0 }  ,draw opacity=0.45 ][fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.17 ] (101,135.24) .. controls (101,119.45) and (113.8,106.65) .. (129.59,106.65) .. controls (145.38,106.65) and (158.18,119.45) .. (158.18,135.24) .. controls (158.18,151.03) and (145.38,163.83) .. (129.59,163.83) .. controls (113.8,163.83) and (101,151.03) .. (101,135.24) -- cycle ;
%Shape: Ellipse [id:dp5086104864165795]
\draw  [color={rgb, 255:red, 155; green, 155; blue, 155 }  ,draw opacity=1 ][fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.17 ] (127.4,155.26) .. controls (127.4,137.96) and (136.31,123.94) .. (147.31,123.94) .. controls (158.3,123.94) and (167.21,137.96) .. (167.21,155.26) .. controls (167.21,172.55) and (158.3,186.57) .. (147.31,186.57) .. controls (136.31,186.57) and (127.4,172.55) .. (127.4,155.26) -- cycle ;
%Shape: Ellipse [id:dp17787031647029328]
\draw  [color={rgb, 255:red, 155; green, 155; blue, 155 }  ,draw opacity=1 ][fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.17 ] (172.16,170.88) .. controls (172.16,154.49) and (177.39,141.21) .. (183.83,141.21) .. controls (190.27,141.21) and (195.5,154.49) .. (195.5,170.88) .. controls (195.5,187.26) and (190.27,200.54) .. (183.83,200.54) .. controls (177.39,200.54) and (172.16,187.26) .. (172.16,170.88) -- cycle ;
%Shape: Ellipse [id:dp1620550175017974]
\draw  [color={rgb, 255:red, 155; green, 155; blue, 155 }  ,draw opacity=1 ][fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.17 ] (241.35,179.82) .. controls (241.35,163.43) and (243.02,150.15) .. (245.09,150.15) .. controls (247.16,150.15) and (248.83,163.43) .. (248.83,179.82) .. controls (248.83,196.2) and (247.16,209.48) .. (245.09,209.48) .. controls (243.02,209.48) and (241.35,196.2) .. (241.35,179.82) -- cycle ;
%Shape: Ellipse [id:dp9944985041379661]
\draw  [color={rgb, 255:red, 155; green, 155; blue, 155 }  ,draw opacity=1 ][fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.17 ] (207.06,176.99) .. controls (207.06,160.61) and (210.61,147.33) .. (214.99,147.33) .. controls (219.37,147.33) and (222.92,160.61) .. (222.92,176.99) .. controls (222.92,193.38) and (219.37,206.66) .. (214.99,206.66) .. controls (210.61,206.66) and (207.06,193.38) .. (207.06,176.99) -- cycle ;
\end{tikzpicture}
</center>---
}
### 其他空间上的复分析
\item<+-> $T^2$ 可以通过 $\mathbb{C}$ 上区域来构造
\item<+-> 考虑 $\mathbb{C}$ 中平行四边形
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.7]
%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300
%Straight Lines [id:da10747870786511204]
\draw [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]    (188,218) -- (326.73,218) ;
%Straight Lines [id:da45122784517615466]
\draw [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][line width=1.5]    (188,218) -- (249.73,133.81) ;
%Straight Lines [id:da014981078186836028]
\draw [line width=1.5]    (249.73,133.81) -- (388.46,133.81) ;
%Straight Lines [id:da7671151316441549]
\draw [line width=1.5]    (326.73,218) -- (388.46,133.81) ;
%Straight Lines [id:da6989813376107556]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (326.73,218) -- (465.46,218) ;
%Straight Lines [id:da04349987243326625]
\draw [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][line width=1.5]    (326.73,218) -- (388.46,133.81) ;
%Straight Lines [id:da4776136593827778]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (388.46,133.81) -- (527.19,133.81) ;
%Straight Lines [id:da9116459600767601]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (465.46,218) -- (527.19,133.81) ;
%Straight Lines [id:da9125430357882869]
\draw [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (249.73,133.81) -- (388.46,133.81) ;
%Straight Lines [id:da5207403314732928]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (311.46,49.61) -- (450.19,49.61) ;
%Straight Lines [id:da483848940142529]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (388.46,133.81) -- (450.19,49.61) ;
%Straight Lines [id:da4483355035529746]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (249.73,133.81) -- (311.46,49.61) ;
%Straight Lines [id:da9321959990077178]
\draw [color={rgb, 255:red, 0; green, 0; blue, 0 }  ,draw opacity=1 ][line width=1.5]    (257.36,218) -- (319.09,133.81) ;
% Text Node
\draw (247.73,130.41) node [anchor=south east] [inner sep=0.75pt]    {$\tau $};
% Text Node
\draw (186,221.4) node [anchor=north east] [inner sep=0.75pt]    {$0$};
% Text Node
\draw (328.73,221.4) node [anchor=north west][inner sep=0.75pt]    {$1$};
% Text Node
\draw (390.46,137.21) node [anchor=north west][inner sep=0.75pt]    {$\tau +1$};
\end{tikzpicture}
</center>---
}
### 其他空间上的复分析
\item<+-> 把左右边粘起来，得到一个圆筒
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1]
%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300
%Straight Lines [id:da736050882014675]
\draw [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][line width=1.5]    (109,181) -- (170.73,96.81) ;
%Straight Lines [id:da777574551902118]
\draw [line width=1.5]    (247.73,181) -- (309.46,96.81) ;
%Straight Lines [id:da37082558640040375]
\draw [line width=1.5]    (247.73,181) -- (309.46,96.81) ;
%Shape: Ellipse [id:dp8853802287549306]
\draw  [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]  (170.73,96.81) .. controls (170.73,85.76) and (201.79,76.81) .. (240.09,76.81) .. controls (278.4,76.81) and (309.46,85.76) .. (309.46,96.81) .. controls (309.46,107.85) and (278.4,116.81) .. (240.09,116.81) .. controls (201.79,116.81) and (170.73,107.85) .. (170.73,96.81) -- cycle ;
%Shape: Ellipse [id:dp2645066019048772]
\draw  [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]  (109,181) .. controls (109,169.95) and (140.06,161) .. (178.36,161) .. controls (216.67,161) and (247.73,169.95) .. (247.73,181) .. controls (247.73,192.05) and (216.67,201) .. (178.36,201) .. controls (140.06,201) and (109,192.05) .. (109,181) -- cycle ;
\end{tikzpicture}
</center>---
}
### 其他空间上的复分析
\item<+-> 把左右边粘起来，得到一个圆筒
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.7]
%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300
%Shape: Arc [id:dp5977308205469367]
\draw  [draw opacity=0][line width=1.5]  (187.15,236.52) .. controls (91.13,225.58) and (22,194.92) .. (22,158.81) .. controls (22,113.52) and (130.73,76.81) .. (264.86,76.81) .. controls (399,76.81) and (507.73,113.52) .. (507.73,158.81) .. controls (507.73,194.92) and (438.6,225.58) .. (342.58,236.52) -- (264.86,158.81) -- cycle ; \draw  [line width=1.5]  (187.15,236.52) .. controls (91.13,225.58) and (22,194.92) .. (22,158.81) .. controls (22,113.52) and (130.73,76.81) .. (264.86,76.81) .. controls (399,76.81) and (507.73,113.52) .. (507.73,158.81) .. controls (507.73,194.92) and (438.6,225.58) .. (342.58,236.52) ;
%Shape: Arc [id:dp8543661349517544]
\draw  [draw opacity=0][line width=1.5]  (189.09,172.9) .. controls (182.7,173.91) and (175.9,174.46) .. (168.85,174.46) .. controls (132.25,174.46) and (102.52,159.74) .. (101.99,141.48) -- (168.85,140.99) -- cycle ; \draw  [line width=1.5]  (189.09,172.9) .. controls (182.7,173.91) and (175.9,174.46) .. (168.85,174.46) .. controls (132.25,174.46) and (102.52,159.74) .. (101.99,141.48) ;
%Shape: Ellipse [id:dp6042887045336574]
\draw  [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]  (169.29,204.92) .. controls (169.29,187.47) and (177.29,173.32) .. (187.15,173.32) .. controls (197.02,173.32) and (205.02,187.47) .. (205.02,204.92) .. controls (205.02,222.37) and (197.02,236.52) .. (187.15,236.52) .. controls (177.29,236.52) and (169.29,222.37) .. (169.29,204.92) -- cycle ;
%Shape: Arc [id:dp7807709688178668]
\draw  [draw opacity=0][line width=1.5]  (342.58,173.32) .. controls (348.96,174.34) and (355.76,174.88) .. (362.81,174.88) .. controls (399.42,174.88) and (429.15,160.16) .. (429.67,141.9) -- (362.81,141.41) -- cycle ; \draw  [line width=1.5]  (342.58,173.32) .. controls (348.96,174.34) and (355.76,174.88) .. (362.81,174.88) .. controls (399.42,174.88) and (429.15,160.16) .. (429.67,141.9) ;
%Shape: Ellipse [id:dp3835366841461014]
\draw  [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]  (324.71,204.92) .. controls (324.71,187.47) and (332.71,173.32) .. (342.58,173.32) .. controls (352.44,173.32) and (360.44,187.47) .. (360.44,204.92) .. controls (360.44,222.37) and (352.44,236.52) .. (342.58,236.52) .. controls (332.71,236.52) and (324.71,222.37) .. (324.71,204.92) -- cycle ;
%Shape: Arc [id:dp3223985773576248]
\draw  [draw opacity=0][line width=1.5]  (125.32,165.48) .. controls (130.21,144.99) and (190.82,128.81) .. (264.86,128.81) .. controls (341.33,128.81) and (403.46,146.07) .. (404.71,167.5) -- (264.86,168.16) -- cycle ; \draw  [line width=1.5]  (125.32,165.48) .. controls (130.21,144.99) and (190.82,128.81) .. (264.86,128.81) .. controls (341.33,128.81) and (403.46,146.07) .. (404.71,167.5) ;
\end{tikzpicture}
</center>\item<+-> 最后把上下边 (紫边) 粘起来，得到二维环面。
---
}
### 其他空间上的复分析
\item<+-> 环面上的任何一点都可以用 $\mathbb{C}$ 中平行四边形的点代表。
\item<+-> 环面上继承 $\mathbb{C}$ 的坐标 $z, \bar z$：可以研究二维环面上的函数、极限、可导性等问题
\item<+-> 还可以考虑更加复杂的二维曲面，比如
<center>
![width:560px](image/Riemann-surfaces.jpeg)
</center>\item<+-> 在这些曲面上均可研究解析函数
---
}
### 其他空间上的复分析
\item<+-> 还可以考虑高维空间上的复分析：复几何 (complex geometry), 代数几何 (algebraic geometry)