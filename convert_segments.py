#!/usr/bin/env python3
"""
Script to convert LaTeX segments to Marp format following tex2marp rules
"""

import re
import os

def convert_latex_to_marp(latex_content):
    """Convert LaTeX content to Marp format"""
    
    # Remove document structure commands
    content = re.sub(r'\\begin\{document\}.*?\\end\{document\}', '', latex_content, flags=re.DOTALL)
    content = re.sub(r'\\documentclass.*?\n', '', content)
    content = re.sub(r'\\usepackage.*?\n', '', content)
    content = re.sub(r'\\newcommand.*?\n', '', content)
    content = re.sub(r'\\definecolor.*?\n', '', content)
    content = re.sub(r'\\pgfdeclareimage.*?\n', '', content)
    content = re.sub(r'%.*?\n', '', content)  # Remove comments
    
    # Convert frames to slides
    content = re.sub(r'\\begin\{frame\}.*?\n', '---\n\n', content)
    content = re.sub(r'\\end\{frame\}', '', content)
    
    # Convert frame titles
    content = re.sub(r'\\frametitle\{([^}]+)\}', r'### \1', content)
    
    # Remove transition commands
    content = re.sub(r'\\transfade', '', content)
    
    # Convert itemize environments
    content = re.sub(r'\\begin\{itemize\}', '', content)
    content = re.sub(r'\\end\{itemize\}', '', content)
    content = re.sub(r'\\item<\+->\\s*', '* ', content)
    content = re.sub(r'\\item\\s*', '* ', content)
    
    # Convert align environments
    content = re.sub(r'\\begin\{align\}', '$$', content)
    content = re.sub(r'\\end\{align\}', '$$', content)
    content = re.sub(r'\\begin\{equation\}', '$$', content)
    content = re.sub(r'\\end\{equation\}', '$$', content)
    
    # Convert color boxes
    content = re.sub(r'\\bluebox\{([^}]+)\}', r'**<orange>\1**', content)
    content = re.sub(r'\\greenbox\{([^}]+)\}', r'**<green>\1**', content)
    content = re.sub(r'\\redbox\{([^}]+)\}', r'**<red>\1**', content)
    
    # Convert comment blocks
    def convert_commentblock(match):
        title = match.group(1)
        content = match.group(2).strip()
        return f"\n<div class='proof comment'>\n\n**{title}**\n\n{content}\n</div>\n"
    
    content = re.sub(r'\\commentblock\{([^}]+)\}\{([^}]+)\}', convert_commentblock, content)
    
    # Convert equation blocks
    def convert_equationblock(match):
        title = match.group(1)
        equation = match.group(2).strip()
        return f"\n<div class='proof equation'>\n\n**{title}**\n\n{equation}\n</div>\n"
    
    content = re.sub(r'\\equationblock\{([^}]+)\}\{([^}]+)\}', convert_equationblock, content)
    
    # Convert section titles
    def convert_section_title(match):
        section_num = match.group(1)
        section_title = match.group(2)
        return f"\n<!-- _class: lead -->\n<!-- _backgroundColor: #f4c2c2 -->\n\n# {section_num}\n\n## {section_title}\n"
    
    content = re.sub(r'\\setbeamertemplate.*?\\begin\{frame\}.*?\\Huge\{\\color\{white\}\{\\textbf\{\{([^}]+)\}\}\}\}.*?\\huge\{\\color\{white\}\{\\textbf\{\{([^}]+)\}\}\}\}.*?\\end\{frame\}', convert_section_title, content, flags=re.DOTALL)
    
    # Convert images
    content = re.sub(r'\\includegraphics\[([^]]+)\]\{([^}]+)\}', lambda m: f"![{m.group(1)}]({m.group(2)})", content)
    content = re.sub(r'\\begin\{center\}', '<center>', content)
    content = re.sub(r'\\end\{center\}', '</center>', content)
    content = re.sub(r'\\caption\{([^}]+)\}', r'\1', content)
    content = re.sub(r'\\begin\{figure\}.*?\\centering', '<center>', content)
    content = re.sub(r'\\end\{figure\}', '</center>', content)
    
    # Clean up extra whitespace
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    content = re.sub(r'^\s+', '', content, flags=re.MULTILINE)
    
    return content.strip()

def main():
    """Convert all segments"""
    for i in range(3, 14):  # Convert segments 3-13
        input_file = f"to-convert-{i}.tex"
        output_file = f"converted-{i}.md"
        
        if not os.path.exists(input_file):
            print(f"Warning: {input_file} not found")
            continue
            
        print(f"Converting {input_file} to {output_file}")
        
        with open(input_file, 'r', encoding='utf-8') as f:
            latex_content = f.read()
        
        marp_content = convert_latex_to_marp(latex_content)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(marp_content)
        
        print(f"Converted {input_file} -> {output_file}")

if __name__ == "__main__":
    main()
