---
### 支点：例
\item<+-> 当 $z$ 沿绕着 $b$ 无穷小的闭曲线一圈，出现多值现象：
$$
\left \{
\begin{array}{cc}
\theta_a \to \theta_a, \\
\theta_b \to \theta_b + 2\pi
\end{array}
\right.\
\Rightarrow \quad
w = \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}}
\to \highlight{orange}{-} \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}} \ .\nonumber
$$
\begin{figure}
\includegraphics{image/multivaluedness.pdf}
</center>
---
### 支点：例
\item<+-> 当 $z$ 沿绕着 $a$ 无穷小的闭曲线一圈，出现多值现象：
$$
\theta_a \to \theta_a + 2\pi, \quad \theta_b \to \theta_b  \ \Rightarrow
w = \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}}
\to \highlight{orange}{-} \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}} \ .\nonumber
$$
\begin{figure}
</center>
---
### 支点：例
\item<+-> 当 $z$ 沿绕着 $a, b$ 任意 **<orange>大** 的闭曲线一圈 **<red>没有** 出现多值现象：
$$
\theta_{a,b} \to \theta_{a,b} + 2\pi, \Rightarrow
w = \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}}
\to \highlight{titleblue}{(-)^2} \sqrt{r_a r_b}e^{\frac{i\theta_a}{2}}e^{\frac{i \theta_b}{2}} \ .\nonumber
$$
\begin{figure}
\includegraphics{non-multivaluedness.pdf}
</center>
---
### 支点：例
\item<+-> 总结：
(1) $b$ 是 $w = \sqrt{(z - a)(z - b)}$ 的一阶支点
(2) $a$ 是 $w = \sqrt{(z - a)(z - b)}$ 的一阶支点
(3) $\infty$ **<red>不是** 支点
---
### 支点：例
\item<+-> 考虑 $w(z) = \sqrt{(z - a)(z - b)(z - c)}$，$a,b,c$ 互异
\visible<+->{一阶支点：$\infty, a, b, c$}
\item<+-> 考虑 $w(z) = \sqrt{1 - \frac{1}{z}}$
\visible<+->{$w(z) = \sqrt{1 - \frac{1}{z}} = \sqrt{\frac{z - 1}{z}}$，一阶支点：$0, 1$。$\infty$ **<red>不是** 支点}
\item<+-> 考虑 $w(z) = (z^3 - 1)^{\frac{1}{3}}$
\visible<+->{$w(z) = [(z - 1)(z - e^{\frac{2\pi i }{3}})(z - e^{\frac{4\pi i }{3}})]^{\frac{1}{3}}$，二阶支点：$1$, $e^{\frac{2\pi i}{3}}$, $e^{\frac{4\pi i}{3}}$。$\infty$ **<red>不是** 支点}
---
### 支点：例
\item<+-> 考虑 $w = z^a (z - 1)^b$，$a, b \in \mathbb{Q}$
(1) 若 $a \not\in \mathbb{Z}$，则 $z = 0$ 是支点
(2) 若 $b \not\in \mathbb{Z}$，则 $z = 1$ 是支点
(3) 若 $a + b \not\in \mathbb{Z}$，则 $z = \infty$ 是支点
---
### 支点：例
\item<+-> 考虑 $w = \ln z$
\item<+-> 给定 $z$，所对应的对数 $w$ 不唯一：可以相差任意的 $2\pi i n$。
\item<+-> 支点：$z = 0, \infty$
\item<+-> 不管绕多少圈，都无法返回原值：**<green>超越 (transcendental) 支点**
\item<+-> $\ln z$ 在 $z = 0$ **<orange>无定义**：与根式函数 $w = \sqrt{z}$ 不一样
---
### 支点
\item<+-> 对于解析函数，支点可能是 **<orange>连续点**
\item<+-> 通常认为支点是 **<orange>奇点**：支点处 **<orange>导数** **<red>无法定义**。
\begin{figure}	
\tikzset{every picture/.style={line width=0.75pt}} 
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.8]
\draw  [dash pattern={on 0.84pt off 2.51pt}]  (239.85,123.34) -- (132.92,123.34) ;
\draw  [color={rgb, 255:red, 0; green, 0; blue, 0 }  ,draw opacity=1 ][fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (130.08,123.34) .. controls (130.08,121.77) and (131.35,120.5) .. (132.92,120.5) .. controls (134.49,120.5) and (135.77,121.77) .. (135.77,123.34) .. controls (135.77,124.92) and (134.49,126.19) .. (132.92,126.19) .. controls (131.35,126.19) and (130.08,124.92) .. (130.08,123.34) -- cycle ;
\draw  [color={rgb, 255:red, 0; green, 0; blue, 0 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (205.68,112.14) .. controls (205.68,110.57) and (206.95,109.3) .. (208.52,109.3) .. controls (210.09,109.3) and (211.37,110.57) .. (211.37,112.14) .. controls (211.37,113.72) and (210.09,114.99) .. (208.52,114.99) .. controls (206.95,114.99) and (205.68,113.72) .. (205.68,112.14) -- cycle ;
\draw  [color={rgb, 255:red, 0; green, 0; blue, 0 }  ,draw opacity=1 ][fill={rgb, 255:red, 144; green, 19; blue, 254 }  ,fill opacity=1 ] (206.48,133.74) .. controls (206.48,132.17) and (207.75,130.9) .. (209.32,130.9) .. controls (210.89,130.9) and (212.17,132.17) .. (212.17,133.74) .. controls (212.17,135.32) and (210.89,136.59) .. (209.32,136.59) .. controls (207.75,136.59) and (206.48,135.32) .. (206.48,133.74) -- cycle ;
\draw [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ]   (208.52,112.14) -- (134.9,123.05) ;
\draw [shift={(132.92,123.34)}, rotate = 351.57] [fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ][line width=0.08]  [draw opacity=0] (12,-3) -- (0,0) -- (12,3) -- cycle    ;
\draw [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ]   (209.32,133.74) -- (134.9,123.61) ;
\draw [shift={(132.92,123.34)}, rotate = 7.75] [fill={rgb, 255:red, 144; green, 19; blue, 254 }  ,fill opacity=1 ][line width=0.08]  [draw opacity=0] (12,-3) -- (0,0) -- (12,3) -- cycle    ;
\draw  [draw opacity=0][fill={rgb, 255:red, 144; green, 19; blue, 254 }  ,fill opacity=0.05 ][dash pattern={on 4.5pt off 4.5pt}] (208.5,134.26) .. controls (203.35,171.21) and (171.63,199.65) .. (133.26,199.65) .. controls (91.31,199.65) and (57.3,165.64) .. (57.3,123.68) .. controls (57.3,81.73) and (91.31,47.72) .. (133.26,47.72) .. controls (171.29,47.72) and (202.8,75.66) .. (208.36,112.14) -- (133.26,123.68) -- cycle ; \draw  [dash pattern={on 4.5pt off 4.5pt}] (208.5,134.26) .. controls (203.35,171.21) and (171.63,199.65) .. (133.26,199.65) .. controls (91.31,199.65) and (57.3,165.64) .. (57.3,123.68) .. controls (57.3,81.73) and (91.31,47.72) .. (133.26,47.72) .. controls (171.29,47.72) and (202.8,75.66) .. (208.36,112.14) ;  
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (60.37,121.27) -- (57.45,127.11) -- (54.53,121.27) -- (57.45,124.19) -- cycle ;
\draw (134.92,126.34) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle a$};
\draw (210.52,109.14) node [anchor=south west] [inner sep=0.75pt]  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,opacity=1 ] [align=left] {$\displaystyle a+\Delta z$};
\draw (211.32,136.74) node [anchor=north west][inner sep=0.75pt]  [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,opacity=1 ] [align=left] {$\displaystyle a+\Delta z'$};
\draw (272,65.53) node [anchor=north west][inner sep=0.75pt]   [align=left] {多值性：$\displaystyle f( a+\Delta z)$ 与 $\displaystyle f( a+\Delta z')$\\函数值相差甚远};
\draw (272,126.53) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle  \begin{array}{{>{\displaystyle}l}}
\lim _{\Delta z\rightarrow 0}\frac{f( a+\Delta z) -f( a)}{\Delta z}\\
\ \ \ \ \ \ \ \ \ \ \ \ \neq \lim _{\Delta z'\rightarrow 0}\frac{f( a+\Delta z') -f( a)}{\Delta z'}
\end{array}$};
\end{tikzpicture}
</center>
---
### 割线
\item<+-> 为了 **<orange>禁止** 多值现象，可以从支点出发画一条曲线把 $\mathbb{C}$ 割破，称为 **<green>割线**，\visible<+->{并 **<orange>明令禁止** $z$ 穿过割线。}\visible<+->{与此同时，**<orange>人为** **<orange>选** 定割线外 **<orange>一点** 的函数值作为 **<orange>基准**。}
\visible<+->{$\Rightarrow$ **<orange>唯一确定** 整个连通解析区的函数值，完全 **<orange>杜绝** 多值现象}
<div class='proof comment'>
**多个支点**
\visible<+->{当函数有 **<orange>多个支点** 的时候，需要 **<orange>每个** 支点都连上割线。若有支点无割线，则出现 **<orange>多值漏洞**
</div>
}
---
### 割线：例
\item<+-> 考虑 $w = \sqrt{z - a}$\visible<+->{，支点是 $a, \infty$}
\visible<+->{\begin{figure}
\includegraphics{branch-cut.pdf}
</center>}
---
### 割线：例
\item<+-> 考虑 $w = \sqrt{(z - a)(z - b)}$，\visible<+->{支点是 $a, b$}
\visible<+->{
\begin{figure}
\includegraphics{double-branch-cut.pdf}
</center>
}
---
### 割线
<div class='proof comment'>
**非局域算符**
在未来的物理学习中，会碰到 **<orange>准局域算符** 和连接准局域算符的 **<orange>拓扑线算符**：这些东西跟跟支点、割线非常相似。
\vspace{1em
</div>
关键词：狄拉克磁单极与狄拉克弦，伊辛模型、disorder 算符和 non-invertible defect line
}
---
### 割线：例
\item<+-> 割线不唯一
\item<+-> 再次考虑 $w = \sqrt{(z - a)(z - b)}$，$a \ne b$，支点为 $a,b$
\begin{figure}
\includegraphics{branch-cut-not-unique.pdf}
</center>
---
### 割线：例
\item<+-> 割线不唯一
\item<+-> 考虑 $w = {(z - a)^{\frac{1}{2}}(z - b)^{\frac{1}{3}}}$，$a \ne b$，支点为 $a,b, \infty$
\begin{figure}
\includegraphics{branch-cut-not-unique-2.pdf}
</center>
---
### 割线与基准值
\item<+-> 
<div class='proof comment'>
**选定基准值**
在设置割线时，需要人为 **<orange>选** 定割线外某点的函数值作为基准。一旦选好了，割线外的所有点函数值均可以通过 **<orange>连续性** **<orange>唯一** 确定。
</div>