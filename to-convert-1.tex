% 	\begin{frame}{目录}
% 		\transfade%淡入淡出效果
% 		\tableofcontents[sectionstyle=show/shaded,subsectionstyle=show/shaded/hide] %突出显示当前章节，而其它章节都进行了淡化处理
% 		\addtocounter{framenumber}{-1}  %目录页不计算页码
% 	\end{frame}
% }
% 48.9984, 104.9856, 130.0224
\definecolor{titleblue}{rgb}{.19140625, .41015625, .50796875}
\definecolor{titlegreen}{rgb}{.359375, .59765625, .41015625}

\newcommand\red[1]{{\color{red!80!black}{#1}}}
\newcommand\blue[1]{{\color[rgb]{0.20, 0.43, 0.75}{#1}}}
\newcommand\orange[1]{{\color{orange}{#1}}}
\newcommand\green[1]{{\color[rgb]{.359375, .59765625, .41015625}{#1}}}
\newcommand\gray[1]{{\color{black!50!white}{#1}}}
\newcommand\purple[1]{{\color{Purple}{#1}}}
\newcommand\white[1]{{\color{white}{#1}}}


\newcommand\eqtext[1]{\overset{\mathrm{#1}}{=\joinrel=}}


\newcommand\bluebox[1]{\colorbox{titleblue!90!white}{\color{white}{\textbf{#1}}}}
\newcommand\titlebox[1]{\colorbox{titleblue!90!white}{\huge \color{white}{\textbf{#1}}}}
\newcommand\greenbox[1]{\colorbox[rgb]{.359375, .59765625, .41015625}{\color{white}{\textbf{#1}}}}
\newcommand\redbox[1]{\colorbox{red!70!black}{\color{white}{\textbf{#1}}}}



% \beamertemplatenavigationsymbolsempty
\pgfdeclareimage[width=\paperwidth,height=0.9575\paperheight]{bg}{112.png}



\begin{document}

\begin{frame}
	%\transfade 渐变
	\titlepage % Print the title page as the first slide
\end{frame}




\begin{frame}
	\frametitle{课程概况}

	\begin{itemize}
		\item<+-> 这是一门 \bluebox{数学课}：核心是数学，但是处处渗透物理思想
		\item<+-> 同时也是一门 \bluebox{语言课}：学会用复数说话，用方程描述物理，用特殊函数刻画物理演化
	\end{itemize}
	
\end{frame}


\begin{frame}{}
	\frametitle{课程概况}
	\transfade
	\begin{itemize}
		\item<+-> 数学是一门独立的、有内在生命力、独特价值评价体系的学科
		\commentblock{数学不只是工具}{
		数学为物理提供工具，但数学 \redbox{不仅仅} 是物理的工具。
		}
		\item<+-> 特别地，数学是物理的 \bluebox{语言}：没有数学，物理学家难以进行精确表达思想和传递信息
		\commentblock{普世语言}{
		甚至可能是整个多重宇宙中所有智慧生物的普世语言。
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{课程概况}
	\transfade
	\begin{itemize}
		\item<+-> 反过来，物理也可以为数学反哺许多新奇的思想和生成数学结构
		\commentblock{来自物理的反作用}{
		广义相对论与微分几何，散射振幅与代数几何，杨-米尔斯理论与微分拓扑，超对称与算子代数、代数簇
		}
	\end{itemize}
\end{frame}






\begin{frame}{}
	\frametitle{课程概况}
	\transfade
	\begin{itemize}
		\item<+-> 《数学物理方法》是众多后续课程和科研的基石
		\item<+-> 电动力学、量子力学：数理方程的求解

		统计力学：复变函数、级数、积分技巧

		量子场论：复变函数、积分技巧、数理方程的求解

		广义相对论：数理方程的求解

		通信与信息技术：傅里叶级数、傅里叶变换，$\delta$ 函数
		\item<+-> 现代理论物理可能需要远超本课程所涵盖的内容：微分与代数几何、抽象代数结构、复杂微分方程

	\end{itemize}
\end{frame}

\begin{frame}{}
	\frametitle{课程概况}
	\begin{itemize}
		\item<+-> 数学物理方法：主要介绍两个基本要素
		\begin{itemize}
			\item<+-> 复数与复变函数
			\item<+-> 数理方程与特殊函数 (作为数理方程的解)
		\end{itemize}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{本章概要}
	\transfade
	\begin{itemize}
		\item 数的历史

		\item 复数

		\gray{复数、复数的表示方法、复数的性质}
		\item 点集基础

		\gray{内点、边界点、聚点、特殊点集}

		\item 解析函数

		\gray{复可导性、Cauchy-Riemann 条件、多值函数与支点}
	\end{itemize}
\end{frame}




{\setbeamertemplate{background}{\includegraphics[width=\paperwidth]{image/section-title-pink.png}}%
\begin{frame}

\begin{center}
	\vspace{-0.8em}
	\Huge{\color{white}{\textbf{{一}}}}

	\vspace{0.7em}
	\huge{\color{white}{\textbf{{数的历史}}}}
\end{center}
\end{frame}
}


\begin{frame}{}
	\frametitle{自然数}
	\transfade
	\begin{itemize}
		\item<+-> 自然数 (natural numbers)，$\mathbb{N}$
		\begin{align}
			\highlight{titleblue}{0}, 1, 2, 3, 4, \ldots
		\end{align}
		\item<+-> 用于标记现实物体、事件的数量
	\end{itemize}
\end{frame}






\begin{frame}{}
	\frametitle{自然数}
	\transfade
	\begin{itemize}
		\item<+-> 原始社会用 \bluebox{绳结} 计数
	\end{itemize}
	\begin{figure}
		\centering
		\includegraphics[width=0.5\textwidth]{image/knots.jpg}
		\caption{印加文明的记事绳结：奇普 (khipu)}
	\end{figure}
\end{frame}



\begin{frame}{}
	\frametitle{自然数}
	\transfade
	\begin{itemize}
		\item<+-> 古埃及和巴比伦、古中国、古印度文明逐步出现 \bluebox{书写符号} 来计数，包括阿拉伯数字
	\end{itemize}
	\begin{figure}
		\centering
		\includegraphics[height=0.4\textheight]{image/egyptian.png}
		\caption{古埃及的数字符号 (一划，马蹄，绳索，睡莲，弯曲的手指，青蛙，Heh)}
	\end{figure}
\end{frame}


\begin{frame}{}
	\frametitle{自然数}
	\transfade
	\begin{itemize}
		\item<+-> 现代社会发展出「正」字计数法
	\end{itemize}
	\begin{figure}
		\centering
		\includegraphics[height=0.5\textheight]{image/vote.jpg}
		\includegraphics[height=0.5\textheight]{image/face.jpg}
	\end{figure}
\end{frame}





\begin{frame}{}
	\frametitle{整数}
	\transfade
	\begin{itemize}
		\item<+-> 整数 (integers, ``whole'' in Latin)，$\mathbb{Z}$
		\begin{align}
			\ldots, -5, -4, -3, -2, -1, 0, 1, 2,3,4,5 , \ldots
		\end{align}
		\begin{center}
			\includegraphics[width=0.7\textwidth]{image/-inf.jpg}
		\end{center}
		
	\end{itemize}
\end{frame}




\begin{frame}{}
	\frametitle{整数}
	\transfade
	
	出现在一些早期文明中：古中国、古印度、古希腊

	比较明确地提出整数的概念：毕达哥拉斯学派、欧几里得等学者
	\begin{center}
		\includegraphics[height=0.4\textheight]{image/chinese1.png}
	\end{center}
\end{frame}
