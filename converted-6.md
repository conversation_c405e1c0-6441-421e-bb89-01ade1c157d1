---
}
### 点与点集的相对关系：例子
\item<+-> 下面 A, B, C, D 是点集 $S$ 的什么点？
<center>
\includegraphics{image/lolipop.pdf}
</center>---
}
### 特殊点集
\item<+-> **<green>定义**：设 $S\subset \mathbb{C}$，若 $\forall z \in S$ **<orange>均** 为 $S$ 的 **<orange>内点**，则称 $S$ 为 **<green>开集**
<div class='proof comment'>
**开集的意义**
开集用于刻画、标记 **<orange>邻近关系**。
\begin{figure
</div>
\includegraphics{image/significance-of-open-set.pdf}
</center>}
---
}
### 特殊点集
\item<+-> 邻域是特殊的开集
\item<+-> 开集的具体定义依赖 **<orange>邻域** 本身：倘若邻域使用 **<orange>别的形状** 定义，则开集的具体概念也会跟着改变
\item<+-> 开集公理 $\Rightarrow$ **<green>拓扑结构**
(1) 有限或无限个开集的 **<orange>并** 还是 **<orange>开集**
(2) 有限个开集的 **<orange>交** 还是 **<orange>开集**
(3) $\mathbb{C}$ 与 $\emptyset$ 是 **<orange>开集**
---
}
### 特殊点集
\item<+-> 存在其他满足开集公理的“开集概念” (拓扑结构)
<div class='proof comment'>
**离散拓扑**
当前的开集概念：任何一个点 $z$ 的任意邻域中都有 **<orange>无数个邻居**——**<orange>非孤立性**。
**<orange>离散拓扑**：任何一个点 $z$ 都存在一个 **<orange>最小邻域**，该邻域只包含它自己——**<orange>孤立性**
</div>
---
}
### 特殊点集
\item<+-> **<green>定义**：若开集 $U$ 内任何两点都可以用一条 **<orange>完全** 属于该集合的曲线连接，则称该开集 $U$ 是 **<green>连通** 的。
\item<+-> 非空、**<orange>连通** 的开集称为 **<green>区域 (domain)**
<center>
\includegraphics{image/domain.pdf}
</center>---
}
### 特殊点集
\item<+-> **<green>定义**：区域 $D$ 及其边界 $\partial D$ 的并集称为一个 **<orange>闭域** 或 **<green>$D$ 的闭包**，记作 $\bar D$。
\item<+-> **<green>定义**：若区域 $D$ 内任何 **<orange>简单闭曲线** 的 **<orange>内部** 均属于 $D$，则称 $D$ 是 **<green>单连通** 的，否则称为 **<green>复连通**
<center>
\includegraphics{image/simply-connected.pdf}
</center>{\setbeamertemplate{background}{![](image/section-title-pink.png)}%
---
}
\vspace{-0.8em}
\Huge{\color{white}{\textbf{{四}}}}
\vspace{0.7em}
\huge{\color{white}{\textbf{{解析函数}}}}
</center>}
---
}
### 复变函数定义
\item<+-> 设 $S \subset \mathbb{C}$。若对 $\forall z \in S$，按照某个 **<orange>规则 $f$** 有 **<orange>唯一** 的复数 $w$ 与之对应，则 $f$ 定义了一个 **<green>单值复变函数**，定义域为 $S$。
\item<+-> 设 $S \subset \mathbb{C}$。若对 $\forall z \in S$，按照某个 **<orange>规则 $f$** 有 **<orange>一个或多个** 的复数 $w$ 与之对应，则 $f$ 定义了一个 **<green>多值复变函数**，定义域为 $S$。
\item<+-> 不管是单值还是多值复变函数，都简记为
\begin{align}
w = f(z) \qquad
\text{or}
\qquad
f(z)\ .
\end{align}
---
}
### 复变函数定义
\item<+-> 记 $w = u + i v$，$z = x + i y$，则
\begin{align}
w = f(z) = u(z) + i v(z) = u(x, y) + i v(x, y) \ .
\end{align}
\visible<+->{换言之，一个复变函数相当于 **<orange>两个** **<orange>二元实函数**，分别称为 $f$ 的实部 $\operatorname{Re} f$ 和虚部 $\operatorname{Im} f$。}
---
}
### 极限
\item<+-> **<green>定义**：设 $w = f(z)$ 的 **<orange>定义域是 $S$**，设某点 $z_0$ 为 $S$ 的 **<orange>聚点**。
若 $\exists w_0 \in \mathbb{C}$，s.t. \visible<+->{对 **<orange>$\forall \epsilon > 0$**，}
\visible<+->{都 **<green>$\exists \delta > 0$**}
\visible<+->{s.t. 对 $\forall z \in (N(z_0, \delta) \cap S) - \{z_0\}$} \visible<+->{都有 $|f(z) - w_0| < \epsilon $,}
\visible<+->{则称 $f$ 在 $z_0$ 处的 **<green>极限存在**，并称 $w_0$ 是 $f(z)$ 在 $z_0$ 处的 **<green>极限**，记作 $w_0 = \lim_{z \to z_0} f(z)$。}
\vspace{-0.5em}
<center>
![width:280px](image/limit.png)
![width:280px](image/dio.png)
</center>---
}
### 极限
<div class='proof comment'>
**翻译翻译**
\begin{center
</div>
![width:480px](image/translate.jpeg)
</center>**<orange>极限存在** 就是，对于 **<orange>甲方** 给出的任意小 **<orange>精度要求 $\epsilon$**，我们总能 **<orange>在定义域 $S$ 中** 找到一个 **<orange>足够小的距离 $\delta$**，使得 **<orange>距离以内** 的所有函数值与目标值 $w_0$ 的差别 **<orange>小于** 精度要求 $\epsilon$。
}
---
}
### 极限
<div class='proof comment'>
**$f$ 在 $z_0$ 可以没有定义**
\visible<+->{根据设定 $z_0$ 是 $S$ 的 **<orange>聚点**，而 $S$ 的聚点可以 **<orange>属于** 也可能 **<red>不属于** $S$，因此 $z_0$ 可能不属于 $f$ 的定义域。
</div>
}
<div class='proof comment'>
**$z$ 的地位**
\visible<+->{条件 $\forall z \in (N(z_0, \delta) \cap S) - \{z_0
</div>
$ 强调两件事}
\visible<+->{(1) $z$ 应该在 $f$ 的定义域中，且应该与 $z_0$ 靠得很近 (距离小于 $\delta$)}
\visible<+->{(2) $z \red{\ne} z_0$ }
}
---
}
### 极限
<div class='proof comment'>
**逼近的方向**
若 $S$ 可以从 **<orange>多个方向** 逼近 $z_0$，则极限存在要求 $f$ 在 **<orange>所有** 逼近方向都存在且趋于 **<orange>同一个值 $w_0$**：四面八方极限存在，且四面八方极限相等。这比一元函数的极限存在要 **<red>苛刻得多**。
</div>
---
}
### 连续性
\item<+-> **<green>定义**：设复变函数 $f$ 的定义域是 $S$。考虑 $z_0\in S$，且是 $S$ 的 **<orange>聚点**。
若
\begin{equation}
\lim_{z \to z_0} f(z) \ \text{存在}, \qquad
\text{且} \qquad
\lim_{z \to z_0} f(z) = f(z_0) \ ,
\end{equation}
则称 $f$ 在 $z_0$ 处 **<green>连续**。
<div class='proof comment'>
**$f(z_0)$ 有定义**
由设定 $z_0 \in S$，$f$ 在 $z_0$ 是 **<orange>有定义** 的。连续性要求这个值恰好等于 $f(z)$ 趋于 $z_0$ 的极限。
</div>
\item<+-> 若 $f$ 在 $S$ 中每一点都连续，则称 $f$ 在 **<green>$S$ 上连续**。
---
}
### 可导性
\item<+-> 设 $f$ 定义在 **<orange>区域 $D$** 上。$z_0 \in D$。若
\begin{align}
\lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z} \text{ 存在且有限} \ ,
\end{align}
则称 $f$ 在 $z_0$ 处 **<green>可导** 或 **<green>可微**，而该极限称为 $f$ 在 $z_0$ 处的 **<green>导数** 或者 **<green>微商**，记作
\begin{align}
[f'(z)]_{z = z_0}, \qquad
f'(z_0), \qquad
\frac{df(z)}{dz}\bigg|_{z = z_0}, \qquad
\frac{d}{dz}\bigg|_{z = z_0}f(z) \ .
\end{align}
<div class='proof comment'>
**区域**
定义域是个区域，因此 当 $|\Delta z|$ 足够小的话可以保证 $z + \Delta z \in D$，从而 $f(z + \Delta z)$ **<orange>有定义**。
</div>