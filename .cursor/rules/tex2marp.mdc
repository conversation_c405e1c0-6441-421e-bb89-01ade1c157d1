---
description: how to convert .tex file into .md file for marp slides
globs: 
alwaysApply: false
---
---
description: This file describes the rules for the conversion of .tex file to .md file for building slides with marp.
globs:
alwaysApply: false
---


# Files
- `slides-example.md`: style examples
- `image/` or `images/`: folder for images

# General rules
- Read the `slides-example.md` file for style examples
- DO NOT change the content of the original `.tex` file, only convert the syntax and format into marp syntax
- When converting, do not use python or bash script. Use your own large language model capability to perform the conversion.

# Tasks and workflow
I need to convert the specified `.tex` file into an `.md` file in Marp slide format.

## Task and Workflow:
**Objective:** Convert a specified `.tex` file into a single, well-formatted `.md` file for Marp presentations, using a sectional approach.


**Prerequisites:**
*   Path to the `source.tex` file.
*   (Optional) Additional LaTeX-to-Marp conversion rules.

**Step 1: Segment `source.tex`**

*   **Action:** Analyze `source.tex` and divide it into logical sections (approx. 10-20 Marp slides each).
*   **Output:** A manifest listing the start and end line numbers for each section in `source.tex`.

**Step 2: Script to Split `source.tex`**

*   **Action:** Create a script (e.g., Bash, Batch, Python).
*   **Inputs:** `source.tex`, section manifest (line numbers).
*   **Functionality:** Extract content for each section into separate files.
*   **Output:** `to-convert-1.tex`, `to-convert-2.tex`, ..., `to-convert-n.tex`.
*   **Constraint:** **MANDATORY** generation of `to-convert-n.tex` files. Do not skip.

**Step 3: Convert Individual Segments to Marp**

*   **For each `to-convert-i.tex`:**
    *   **A. Create:** New `converted-i.md` file.
    *   **B. Convert:** Entire content of `to-convert-i.tex` to Marp format in `converted-i.md`.
        *   **STRICTLY Follow** rules in the "Detailed replacemenet rules" section
        *   **Adhere to Marp syntax** for slides, directives, and features.
        *   **Preserve all content:** No omissions of frames, sentences, or math expressions.
        *   **Content Integrity:** No substantive content changes; only format conversion.
        *   **Math:** Use Marp-compatible LaTeX for math (inline math using `$...$`, and `$$...$$` for other math environments).

**Step 4: Verify and Refine Segment Conversion**

*   **For each `converted-i.md`:**
    *   **A. Compare:** `converted-i.md` with its source `to-convert-i.tex`.
    *   **B. Check for:** Omissions, errors (especially math), structural loss.
    *   **C. Correct:** Revise `converted-i.md` to fix issues (add missing frames, fix math errors, etc.), maintaining original structure.

**Step 5: Iterate for All Segments**

*   **Action:** Repeat Step 3 (Convert) and Step 4 (Verify) for all `to-convert-n.tex` files.
*   **Outcome:** A set of verified `converted-1.md`, ..., `converted-n.md` files.

**Step 6: Script to Merge `converted-n.md` Files**

*   **Action:** Create a script.
*   **Inputs:** All `converted-n.md` files.
*   **Functionality:**
    1.  Concatenate `converted-1.md` through `converted-n.md` sequentially.
    2.  **Directive Management:** Ensure only one set of necessary global Marp directives (from `converted-1.md`) exists at the start of the final file. Remove redundant global directives from subsequent files. Maintain slide separators.
*   **Output:** A single `converted.md` file.

---


# Detailed replacemenet rules

## convert itemize environment
- Simply use the markdown `-` to replace the `\item` environment
- Use `*` to replace the `\item<+->` environment

## convert colored box
(the double star should be included in the replacement, with NO SPACE between the stars and the content)
- `\greenbox{green text}` -> `**<green>green text**`
- `\redbox{green text}` -> `**<red>green text**`
- `\bluebox{green text}` -> `**<orange>green text**`

## convert comment block
- `\commentblock{COMMENT_TITLE}{COMMENT_CONTENT}` should be replaced by (notice an EMPTY line should be added AFTER the `<div class='proof comment'>`)

  ```
  <div class='proof comment'>

  **COMMENT_TITLE**

  COMMENT_CONTENT
  </div>
  ```

- In `\commentblock{COMMENT_TITLE}{COMMENT_CONTENT}`, there are also colored boxes, `\greenbox{text}`, `\bluebox{text}`, `\redbox{text}`, also replaceable by `**<green>green text**`, `**<red>red text**`, `**<orange>orange text**`

## convert exposition block
- `\expositionblock{TITLE}{CONTENT}` should be replaced by (notice an EMPTY line should be added AFTER the <div class="proof">)

  ```
  <div class="proof">

  **TITLE**

  CONTENT
  </div>
  ```

## convert highlight in math environment
- In math environment, replace `\highlight{red}{MATH}` by `\red{MATH}` 
- In math environment, replace `\highlight{titlegreen}{MATH}` by `\green{MATH}` 
- In math environment, replace `\highlight{titleblue}{MATH}` by `\orange{MATH}`
- DO NOT CHANGE THE CONTENT OF THE MATH EQUATION, KEEP THE `\begin{align}...\end{align}` and the alignment points `&=`, `=&`, etc.

## convert image
- replace `\includegraphics[width=XX\textwidth]{URL}` with

  ```
  ![width:YYpx](URL)
  ```

  adjust YY based on XX and the width of the slide


## convert pdf to svg
- There could pdf images imported in the .tex file, they should be first converted into .svg files
- Use `inkscape` to convert the pdf files in the `image` or `images` folder, by the following command:
  ```
  for pdf in *.pdf; do inkscape --without-gui --file="$pdf" --export-plain-svg --export-filename="${pdf%.pdf}.svg"; done
  ```
- reference the svg files in the .md file
  ```
  ![width:YYpx](URL)
  ```


## save tikzpicture to standalone tex file
- When encounter a tikzpicture environment in `to-convert-i.tex` file
  ```
  \begin{figure}

			\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt        

			\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, ...]
			  ...
			\end{tikzpicture}
  \end{figure}
  ```
  Save the content 
  ```
  \tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt        

  \begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, ...]
    more tex code
  \end{tikzpicture}
  ```
  to a standalone .tex file under the folder `image/` with a appropriate file name,
  ```
  \documentclass{standalone}
  \usepackage{latexsym,amsmath,amsfonts,amssymb}
  \usepackage[dvipsnames]{xcolor} % To use rich set of colors
  \usepackage{tikz}

  \begin{document}
  \tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt        

  \begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, ...]
    more tex code
  \end{tikzpicture}
  \end{document}
  ```
- Compile the .tex file, and convert the generated pdf file to svg file
  ```
  inkscape --without-gui --file="tikzpicture.pdf" --export-plain-svg --export-filename="tikzpicture.svg";
  ```
- Reference the svg file in the .md file
  ```
  ![width:YYpx](URL)
  ```


## an example .sh file for splitting
```
#!/bin/bash
# 按照 tex2marp 规则，将 blackhole thermaldynamics.tex 按每 15 个 frame 分割成 to-convert-n.tex 文件

INPUT="blackhole thermaldynamics.tex"
FRAME_STARTS=($(grep -n "begin{frame}" "$INPUT" | cut -d: -f1))
FRAME_ENDS=($(grep -n "end{frame}" "$INPUT" | cut -d: -f1))
TOTAL_FRAMES=${#FRAME_STARTS[@]}

FRAMES_PER_FILE=15
TOTAL_PARTS=$(( (TOTAL_FRAMES + FRAMES_PER_FILE - 1) / FRAMES_PER_FILE ))

for ((i=0; i<$TOTAL_PARTS; i++)); do
    PART_START_IDX=$((i * FRAMES_PER_FILE))
    PART_END_IDX=$(( (i+1) * FRAMES_PER_FILE - 1 ))
    if [ $PART_END_IDX -ge $((TOTAL_FRAMES)) ]; then
        PART_END_IDX=$((TOTAL_FRAMES - 1))
    fi
    START_LINE=${FRAME_STARTS[$PART_START_IDX]}
    END_LINE=${FRAME_ENDS[$PART_END_IDX]}
    if [ -z "$START_LINE" ] || [ -z "$END_LINE" ]; then
        continue
    fi
    sed -n "${START_LINE},${END_LINE}p" "$INPUT" > "to-convert-$((i+1)).tex"
done

echo "分割完成，共 $TOTAL_PARTS 个部分。" 
```


## an example .sh file for TASK-9
```
#!/bin/bash

# 1. 提取第一个文件的全局 marp 配置（假设在文件开头的 --- 到 --- 之间）
head -n $(awk '/^---/{i++}i==2{print NR; exit}' converted-1.md) converted-1.md > converted.md

# 2. 依次处理每个 converted-n.md，去除各自的 marp 配置部分，只保留内容
for f in converted-1.md converted-2.md converted-3.md; do
  # 跳过第一个文件的 marp 配置（已添加）
  if [ "$f" = "converted-1.md" ]; then
    # 只保留第一个文件的 marp 配置
    tail -n +$(($(awk '/^---/{i++}i==2{print NR; exit}' $f) + 1)) $f >> converted.md
  else
    # 其他文件去掉开头的 marp 配置
    tail -n +$(($(awk '/^---/{i++}i==2{print NR; exit}' $f) + 1)) $f >> converted.md
  fi
done 
```