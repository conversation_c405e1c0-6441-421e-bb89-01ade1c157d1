---
### 指数表示下的乘除运算
\item<+-> 在代数式表示法中，乘法和除法的表达式都非常复杂。
\item<+-> **<orange>定理**：考虑 $z_1 = r_1 e^{i \theta_1}$，$z_2 = r_2 e^{i \theta_2}$。则
$$
z_1 z_2 = r_1 r_2 e^{i (\theta_1 + \theta_2)}, \qquad
\frac{z_1}{z_2} = \frac{r_1}{r_2} e^{i (\theta_1 - \theta_2)} \ .
$$
\expositionblock{证明}{
利用三角表示法：$z_{1,2} = r_1 (\cos\theta_{1,2} + i \sin \theta_{1,2})$，得到
$$
z_1 z_2
= & \ r_1 r_2 (\cos \theta_1 + i \sin \theta_1)(\cos \theta_2 + i \sin \theta_2) \nonumber\\
= & \ \visible<+->{r_1 r_2 (\cos \theta_1 \cos\theta_2 - \sin \theta_1 \sin\theta_2
+ i \sin \theta_1 \cos \theta_2 + i \cos \theta_1 \sin \theta_2
)} \nonumber \ .
$$
}
---
### 指数表示下的乘除运算
\expositionblock{证明}{
利用三角函数的积化和差，得到
$$
z_1 z_2
= & \ r_1 r_2 [\cos (\theta_1 + \theta_2) + i \sin (\theta_1 + \theta_2)]
= r_1 r_2 e^{i (\theta_1 + \theta_2)} \ .
$$
}
---
### 指数表示下的乘除运算
\expositionblock{证明}{
对于除法，
$$
\frac{z_1}{z_2} = & \ \frac{r_1 \cos \theta_1 + i r_1 \sin \theta_1}{r_2 \cos \theta_2 + i r_2 \sin \theta_2}\\
= & \ \frac{r_1 r_2 \cos\theta_1 \cos \theta_2 + r_1 r_2 \sin\theta_1 \sin \theta_2}{r_2^2\cos^2\theta_2 + r_2^2\sin^2\theta_2} \\
& \ + \frac{- r_1 r_2 \cos\theta_1 \sin \theta_2 + r_1 r_2 \sin\theta_1 \cos \theta_2}{r_2^2\cos^2\theta_2 + r_2^2\sin^2\theta_2}\\
= & \ \frac{r_1 r_2 \cos(\theta_1 - \theta_2)}{r_2^2}
+ \frac{r_1 r_2 \sin (\theta_1 - \theta_2)}{r_2^2} = \frac{r_1}{r_2} e^{i (\theta_1 - \theta_2)} \ .
$$
}
---
### 总结
\item<+-> 三种常用表示：代数表示、三角表示、指数表示
$$
z = x + i y = r \cos\theta + i r \sin \theta = r e^{i \theta} \ .
$$
\item<+-> 模长 $|z|$
$$
|z| = r = \sqrt{(r \cos\theta)^2 + (r \sin \theta)^2} = \sqrt{x^2 + y^2}
= \sqrt{ z \bar z}
$$
模方 (modulus squared)
$$
|z|^2 = z \bar z \ .
$$
---
### 总结
\item<+-> $| - z| = |z|$
\item<+-> $|e^{i \theta}| = 1$，其中 $\theta \in \mathbb{R}$
\item<+-> $|z_1 z_2| = |z_1| |z_2|$
---
### 代数基本定理
\item<+-> **<orange>代数基本定理**：任何一个 $n$-次复系数多项式都有 $n$ 个复数根
<div class='proof comment'>
**重根**
这 $n$ 个复数根可能有 **<orange>重复**。
</div>
<div class='proof comment'>
**因式分解**
$n$-次复系数多项式 $a_n x^n + a_{n - 1
</div>
z^{n - 1} + ... + a_0$ 一定可以分解为
$$
P(z) = a_n(z - z_1) (z - z_2) \cdots (z - z_n) \ ,
$$
其中 $z_i$ 为根。
}
---
### 代数基本定理
\item<+-> **<orange>韦达定理**：对任意复多项式 $P(z) = a_n z^n + a_{n - 1}z^{n - 1} + \cdots + a_0$ 的根 $z_1, \cdots, z_n$ 满足
$$
(-1)^n a_n \prod_{k = 1}^{n}z_k = a_0, \qquad 
a_n\sum_{k = 1}^{n}z_k = - a_{n - 1} \ .
$$
---
### 无穷远点
\item<+-> $\mathbb{R}$ 与无穷远点
<center>
\href{./animation/ExtendedR1.html}{
![width=0.6\textwidth](animation/ExtendedR1.png)
}
</center>
\item<+-> $\mathbb{R} \cup \infty$ 实际上就是一维圆圈 $S^1$。
---
### 无穷远点
\item<+-> $\mathbb{C}$ 与无穷远点 $\infty$ 合并为 **<green>扩充的复平面**
\item<+-> $\mathbb{C} \cup \{\infty\}$ 实际上就是二维球面 $S^2$。
---
\begin{figure}
\centering
\includegraphics{image/extended-C.pdf}
</center>
{\setbeamertemplate{background}{![width=\paperwidth](image/section-title-pink.png)}---
<center>
\vspace{-0.8em}
\Huge{\color{white}{\textbf{{三}}}}
\vspace{0.7em}
\huge{\color{white}{\textbf{{点集基础}}}}
</center>
}
---
### 点集基础
\item<+-> 复变函数的定义域是 $\mathbb{C}$ 中的点集
\item<+-> 需要清楚 $\mathbb{C}$ 中点集的性质
\item<+-> 常用符号
s.t. = 「使得」
$\forall$ (Any) = 「任意」
$\exists$ (Exists) = 「存在」
$\in$ = 「属于」，$\subset$ = 是子集
$\epsilon$ = 「希腊字母 epsilon」
---
### 邻域
\item<+-> **<green>定义**：以 $z_0$ 为中心半径为 $r > 0$ 的 **<green>邻域 (neighborhood)**
$$
N(z_0, r) \coloneqq \{z \in \mathbb{C} \ | \ |z - z_0| < r\} \ .
$$
\item<+-> 直观上是以 $z_0$ 为中心 $r$ 为半径的 **<orange>开** 圆盘
\begin{figure}
\includegraphics{image/open-disk.pdf}
</center>
\item<+-> 也可以用其他 **<orange>多边形** 的 **<orange>内部** 作为定义：$\triangle$, $\blacksquare$, ...
\item<+-> 是下面所有讨论的基础
---
### 点与点集的相对关系：内点
\item<+-> 下面讨论点与点集的 **<orange>相对关系**
---
### 点与点集的相对关系：内点
\item<+-> **<green>定义**：考虑集合 $S \subset \mathbb{C}$，以及点 $z_0 \in \mathbb{C}$。倘若 **<orange>存在** $\epsilon > 0$ 使得 $N(z_0, \epsilon) \subset S$，则称 $z_0$ 为 $S $ 的一个 **<green>内点**
\begin{figure}
\centering
\includegraphics{image/inner-point.pdf}
</center>