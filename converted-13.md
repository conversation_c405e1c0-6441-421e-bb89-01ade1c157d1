---
}
### 其他空间上的复分析
\item<+-> 例子：二维环面 $T^2$
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.7]
%uncomment if require: \path (0,241); %set diagram left start at 0, and has height of 241
%Shape: Ellipse [id:dp9448478903864244]
\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=0.01 ][line width=1.5]  (101,135.24) .. controls (101,94.24) and (170.88,61) .. (257.09,61) .. controls (343.3,61) and (413.18,94.24) .. (413.18,135.24) .. controls (413.18,176.24) and (343.3,209.48) .. (257.09,209.48) .. controls (170.88,209.48) and (101,176.24) .. (101,135.24) -- cycle ;
%Shape: Arc [id:dp25432641807910294]
\draw  [draw opacity=0][line width=1.5]  (360.13,119.29) .. controls (360.16,119.61) and (360.18,119.92) .. (360.18,120.24) .. controls (360.18,136.94) and (314.25,150.48) .. (257.59,150.48) .. controls (202.01,150.48) and (156.75,137.45) .. (155.05,121.19) -- (257.59,120.24) -- cycle ; \draw  [line width=1.5]  (360.13,119.29) .. controls (360.16,119.61) and (360.18,119.92) .. (360.18,120.24) .. controls (360.18,136.94) and (314.25,150.48) .. (257.59,150.48) .. controls (202.01,150.48) and (156.75,137.45) .. (155.05,121.19) ;
%Shape: Arc [id:dp6163497515775678]
\draw  [draw opacity=0][line width=1.5]  (175.03,139) .. controls (175.01,138.75) and (175,138.49) .. (175,138.24) .. controls (175,122.64) and (211.75,110) .. (257.09,110) .. controls (301.69,110) and (337.98,122.24) .. (339.15,137.48) -- (257.09,138.24) -- cycle ; \draw  [line width=1.5]  (175.03,139) .. controls (175.01,138.75) and (175,138.49) .. (175,138.24) .. controls (175,122.64) and (211.75,110) .. (257.09,110) .. controls (301.69,110) and (337.98,122.24) .. (339.15,137.48) ;
%Shape: Circle [id:dp6696950641438213]
\draw  [color={rgb, 255:red, 0; green, 0; blue, 0 }  ,draw opacity=0.45 ][fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.17 ] (101,135.24) .. controls (101,119.45) and (113.8,106.65) .. (129.59,106.65) .. controls (145.38,106.65) and (158.18,119.45) .. (158.18,135.24) .. controls (158.18,151.03) and (145.38,163.83) .. (129.59,163.83) .. controls (113.8,163.83) and (101,151.03) .. (101,135.24) -- cycle ;
%Shape: Ellipse [id:dp5086104864165795]
\draw  [color={rgb, 255:red, 155; green, 155; blue, 155 }  ,draw opacity=1 ][fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.17 ] (127.4,155.26) .. controls (127.4,137.96) and (136.31,123.94) .. (147.31,123.94) .. controls (158.3,123.94) and (167.21,137.96) .. (167.21,155.26) .. controls (167.21,172.55) and (158.3,186.57) .. (147.31,186.57) .. controls (136.31,186.57) and (127.4,172.55) .. (127.4,155.26) -- cycle ;
%Shape: Ellipse [id:dp17787031647029328]
\draw  [color={rgb, 255:red, 155; green, 155; blue, 155 }  ,draw opacity=1 ][fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.17 ] (172.16,170.88) .. controls (172.16,154.49) and (177.39,141.21) .. (183.83,141.21) .. controls (190.27,141.21) and (195.5,154.49) .. (195.5,170.88) .. controls (195.5,187.26) and (190.27,200.54) .. (183.83,200.54) .. controls (177.39,200.54) and (172.16,187.26) .. (172.16,170.88) -- cycle ;
%Shape: Ellipse [id:dp1620550175017974]
\draw  [color={rgb, 255:red, 155; green, 155; blue, 155 }  ,draw opacity=1 ][fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.17 ] (241.35,179.82) .. controls (241.35,163.43) and (243.02,150.15) .. (245.09,150.15) .. controls (247.16,150.15) and (248.83,163.43) .. (248.83,179.82) .. controls (248.83,196.2) and (247.16,209.48) .. (245.09,209.48) .. controls (243.02,209.48) and (241.35,196.2) .. (241.35,179.82) -- cycle ;
%Shape: Ellipse [id:dp9944985041379661]
\draw  [color={rgb, 255:red, 155; green, 155; blue, 155 }  ,draw opacity=1 ][fill={rgb, 255:red, 155; green, 155; blue, 155 }  ,fill opacity=0.17 ] (207.06,176.99) .. controls (207.06,160.61) and (210.61,147.33) .. (214.99,147.33) .. controls (219.37,147.33) and (222.92,160.61) .. (222.92,176.99) .. controls (222.92,193.38) and (219.37,206.66) .. (214.99,206.66) .. controls (210.61,206.66) and (207.06,193.38) .. (207.06,176.99) -- cycle ;
\end{tikzpicture}
</center>---
}
### 其他空间上的复分析
\item<+-> $T^2$ 可以通过 $\mathbb{C}$ 上区域来构造
\item<+-> 考虑 $\mathbb{C}$ 中平行四边形
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.7]
%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300
%Straight Lines [id:da10747870786511204]
\draw [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]    (188,218) -- (326.73,218) ;
%Straight Lines [id:da45122784517615466]
\draw [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][line width=1.5]    (188,218) -- (249.73,133.81) ;
%Straight Lines [id:da014981078186836028]
\draw [line width=1.5]    (249.73,133.81) -- (388.46,133.81) ;
%Straight Lines [id:da7671151316441549]
\draw [line width=1.5]    (326.73,218) -- (388.46,133.81) ;
%Straight Lines [id:da6989813376107556]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (326.73,218) -- (465.46,218) ;
%Straight Lines [id:da04349987243326625]
\draw [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][line width=1.5]    (326.73,218) -- (388.46,133.81) ;
%Straight Lines [id:da4776136593827778]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (388.46,133.81) -- (527.19,133.81) ;
%Straight Lines [id:da9116459600767601]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (465.46,218) -- (527.19,133.81) ;
%Straight Lines [id:da9125430357882869]
\draw [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (249.73,133.81) -- (388.46,133.81) ;
%Straight Lines [id:da5207403314732928]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (311.46,49.61) -- (450.19,49.61) ;
%Straight Lines [id:da483848940142529]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (388.46,133.81) -- (450.19,49.61) ;
%Straight Lines [id:da4483355035529746]
\draw [line width=1.5]  [dash pattern={on 5.63pt off 4.5pt}]  (249.73,133.81) -- (311.46,49.61) ;
%Straight Lines [id:da9321959990077178]
\draw [color={rgb, 255:red, 0; green, 0; blue, 0 }  ,draw opacity=1 ][line width=1.5]    (257.36,218) -- (319.09,133.81) ;
% Text Node
\draw (247.73,130.41) node [anchor=south east] [inner sep=0.75pt]    {$\tau $};
% Text Node
\draw (186,221.4) node [anchor=north east] [inner sep=0.75pt]    {$0$};
% Text Node
\draw (328.73,221.4) node [anchor=north west][inner sep=0.75pt]    {$1$};
% Text Node
\draw (390.46,137.21) node [anchor=north west][inner sep=0.75pt]    {$\tau +1$};
\end{tikzpicture}
</center>---
}
### 其他空间上的复分析
\item<+-> 把左右边粘起来，得到一个圆筒
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1]
%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300
%Straight Lines [id:da736050882014675]
\draw [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][line width=1.5]    (109,181) -- (170.73,96.81) ;
%Straight Lines [id:da777574551902118]
\draw [line width=1.5]    (247.73,181) -- (309.46,96.81) ;
%Straight Lines [id:da37082558640040375]
\draw [line width=1.5]    (247.73,181) -- (309.46,96.81) ;
%Shape: Ellipse [id:dp8853802287549306]
\draw  [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]  (170.73,96.81) .. controls (170.73,85.76) and (201.79,76.81) .. (240.09,76.81) .. controls (278.4,76.81) and (309.46,85.76) .. (309.46,96.81) .. controls (309.46,107.85) and (278.4,116.81) .. (240.09,116.81) .. controls (201.79,116.81) and (170.73,107.85) .. (170.73,96.81) -- cycle ;
%Shape: Ellipse [id:dp2645066019048772]
\draw  [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]  (109,181) .. controls (109,169.95) and (140.06,161) .. (178.36,161) .. controls (216.67,161) and (247.73,169.95) .. (247.73,181) .. controls (247.73,192.05) and (216.67,201) .. (178.36,201) .. controls (140.06,201) and (109,192.05) .. (109,181) -- cycle ;
\end{tikzpicture}
</center>---
}
### 其他空间上的复分析
\item<+-> 把左右边粘起来，得到一个圆筒
<center>
\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt
\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.7]
%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300
%Shape: Arc [id:dp5977308205469367]
\draw  [draw opacity=0][line width=1.5]  (187.15,236.52) .. controls (91.13,225.58) and (22,194.92) .. (22,158.81) .. controls (22,113.52) and (130.73,76.81) .. (264.86,76.81) .. controls (399,76.81) and (507.73,113.52) .. (507.73,158.81) .. controls (507.73,194.92) and (438.6,225.58) .. (342.58,236.52) -- (264.86,158.81) -- cycle ; \draw  [line width=1.5]  (187.15,236.52) .. controls (91.13,225.58) and (22,194.92) .. (22,158.81) .. controls (22,113.52) and (130.73,76.81) .. (264.86,76.81) .. controls (399,76.81) and (507.73,113.52) .. (507.73,158.81) .. controls (507.73,194.92) and (438.6,225.58) .. (342.58,236.52) ;
%Shape: Arc [id:dp8543661349517544]
\draw  [draw opacity=0][line width=1.5]  (189.09,172.9) .. controls (182.7,173.91) and (175.9,174.46) .. (168.85,174.46) .. controls (132.25,174.46) and (102.52,159.74) .. (101.99,141.48) -- (168.85,140.99) -- cycle ; \draw  [line width=1.5]  (189.09,172.9) .. controls (182.7,173.91) and (175.9,174.46) .. (168.85,174.46) .. controls (132.25,174.46) and (102.52,159.74) .. (101.99,141.48) ;
%Shape: Ellipse [id:dp6042887045336574]
\draw  [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]  (169.29,204.92) .. controls (169.29,187.47) and (177.29,173.32) .. (187.15,173.32) .. controls (197.02,173.32) and (205.02,187.47) .. (205.02,204.92) .. controls (205.02,222.37) and (197.02,236.52) .. (187.15,236.52) .. controls (177.29,236.52) and (169.29,222.37) .. (169.29,204.92) -- cycle ;
%Shape: Arc [id:dp7807709688178668]
\draw  [draw opacity=0][line width=1.5]  (342.58,173.32) .. controls (348.96,174.34) and (355.76,174.88) .. (362.81,174.88) .. controls (399.42,174.88) and (429.15,160.16) .. (429.67,141.9) -- (362.81,141.41) -- cycle ; \draw  [line width=1.5]  (342.58,173.32) .. controls (348.96,174.34) and (355.76,174.88) .. (362.81,174.88) .. controls (399.42,174.88) and (429.15,160.16) .. (429.67,141.9) ;
%Shape: Ellipse [id:dp3835366841461014]
\draw  [color={rgb, 255:red, 144; green, 19; blue, 254 }  ,draw opacity=1 ][line width=1.5]  (324.71,204.92) .. controls (324.71,187.47) and (332.71,173.32) .. (342.58,173.32) .. controls (352.44,173.32) and (360.44,187.47) .. (360.44,204.92) .. controls (360.44,222.37) and (352.44,236.52) .. (342.58,236.52) .. controls (332.71,236.52) and (324.71,222.37) .. (324.71,204.92) -- cycle ;
%Shape: Arc [id:dp3223985773576248]
\draw  [draw opacity=0][line width=1.5]  (125.32,165.48) .. controls (130.21,144.99) and (190.82,128.81) .. (264.86,128.81) .. controls (341.33,128.81) and (403.46,146.07) .. (404.71,167.5) -- (264.86,168.16) -- cycle ; \draw  [line width=1.5]  (125.32,165.48) .. controls (130.21,144.99) and (190.82,128.81) .. (264.86,128.81) .. controls (341.33,128.81) and (403.46,146.07) .. (404.71,167.5) ;
\end{tikzpicture}
</center>\item<+-> 最后把上下边 (紫边) 粘起来，得到二维环面。
---
}
### 其他空间上的复分析
\item<+-> 环面上的任何一点都可以用 $\mathbb{C}$ 中平行四边形的点代表。
\item<+-> 环面上继承 $\mathbb{C}$ 的坐标 $z, \bar z$：可以研究二维环面上的函数、极限、可导性等问题
\item<+-> 还可以考虑更加复杂的二维曲面，比如
<center>
![width:560px](image/Riemann-surfaces.jpeg)
</center>\item<+-> 在这些曲面上均可研究解析函数
---
}
### 其他空间上的复分析
\item<+-> 还可以考虑高维空间上的复分析：复几何 (complex geometry), 代数几何 (algebraic geometry)