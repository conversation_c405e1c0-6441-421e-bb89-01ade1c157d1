#!/bin/bash
# <PERSON>ript to split slides.tex into segments according to tex2marp rules

INPUT="slides.tex"

# Get all frame start and end line numbers (including commented frames for proper pairing)
FRAME_STARTS=($(grep -n "\\\\begin{frame}" "$INPUT" | cut -d: -f1))
FRAME_ENDS=($(grep -n "\\\\end{frame}" "$INPUT" | cut -d: -f1))

TOTAL_FRAMES=${#FRAME_STARTS[@]}
echo "Total frames found: $TOTAL_FRAMES"
echo "Frame starts: ${#FRAME_STARTS[@]}, Frame ends: ${#FRAME_ENDS[@]}"

# Verify frame pairing
if [ ${#FRAME_STARTS[@]} -ne ${#FRAME_ENDS[@]} ]; then
    echo "Warning: Mismatch between frame starts and ends!"
    echo "Frame starts: ${#FRAME_STARTS[@]}, Frame ends: ${#FRAME_ENDS[@]}"
fi

FRAMES_PER_SEGMENT=15
TOTAL_SEGMENTS=$(( (TOTAL_FRAMES + FRAMES_PER_SEGMENT - 1) / FRAMES_PER_SEGMENT ))

echo "Creating $TOTAL_SEGMENTS segments with approximately $FRAMES_PER_SEGMENT frames each"

# Create segments dynamically
for ((i=0; i<$TOTAL_SEGMENTS; i++)); do
    SEGMENT_NUM=$((i + 1))
    START_IDX=$((i * FRAMES_PER_SEGMENT))
    END_IDX=$(( (i+1) * FRAMES_PER_SEGMENT - 1 ))

    # Ensure we don't exceed array bounds
    if [ $END_IDX -ge $TOTAL_FRAMES ]; then
        END_IDX=$((TOTAL_FRAMES - 1))
    fi

    # Get line numbers for this segment
    START_LINE=${FRAME_STARTS[$START_IDX]}
    END_LINE=${FRAME_ENDS[$END_IDX]}

    if [ -z "$START_LINE" ] || [ -z "$END_LINE" ]; then
        echo "Warning: Could not determine line numbers for segment $SEGMENT_NUM"
        echo "START_IDX=$START_IDX, END_IDX=$END_IDX"
        echo "START_LINE=$START_LINE, END_LINE=$END_LINE"
        continue
    fi

    ACTUAL_FRAMES=$((END_IDX - START_IDX + 1))
    echo "Creating segment $SEGMENT_NUM: frames $((START_IDX + 1))-$((END_IDX + 1)) (lines $START_LINE-$END_LINE, $ACTUAL_FRAMES frames)"

    # Extract the segment
    sed -n "${START_LINE},${END_LINE}p" "$INPUT" > "to-convert-${SEGMENT_NUM}.tex"

    echo "Created to-convert-${SEGMENT_NUM}.tex"
done

echo "Segmentation complete. Created $TOTAL_SEGMENTS segments."
