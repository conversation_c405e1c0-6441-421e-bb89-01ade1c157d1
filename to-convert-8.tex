\begin{frame}{}
	\frametitle{复导数}
	\transfade
	\begin{itemize}
		\item<+-> $z$ 与 $\bar z$ \redbox{不独立}：$z \to z + \Delta \Rightarrow \bar z \to \bar z + \overline{\Delta z}$。
		\item<+-> 但是，偏导算符呈现 \bluebox{形式上独立性 (formal independence)}，
		\begin{align}
			\frac{\partial \bar z}{\partial z} = \frac{\partial z}{\partial \bar z} = 0, \qquad
			\frac{\partial z}{\partial z} = \frac{\partial \bar z}{\partial \bar z} = 1\ .
		\end{align}
		\expositionblock{证明}{
		直接计算。比如，结合 $z = x + iy$, $\bar z = x - i y$，
		\begin{align}
			\frac{\partial \bar z}{\partial z}
			= & \ \frac{1}{2} \left[\frac{\partial (x- i y)}{\partial x} - i \frac{\partial (x- i y)}{\partial y}\right]
			= \frac{1}{2} \left[1 - i (-i)\right] = 0 \\
			\frac{\partial z}{\partial z}
			= & \ \frac{1}{2} \left[\frac{\partial (x + i y)}{\partial x} - i \frac{\partial (x + i y)}{\partial y}\right]
			= \frac{1}{2} \left[1 - i (+i)\right] = 1 \ .
		\end{align}
		}

		
	\end{itemize}
\end{frame}

\begin{frame}{}
	\frametitle{复导数}
	\transfade
	\begin{itemize}
		\item $z$ 与 $\bar z$ \redbox{不独立}：$z \to z + \Delta \Rightarrow \bar z \to \bar z + \overline{\Delta z}$。
		\item 但是，偏导算符呈现 \bluebox{形式上独立性 (formal independence)}，
		\begin{align}
			\frac{\partial \bar z}{\partial z} = \frac{\partial z}{\partial \bar z} = 0, \qquad
			\frac{\partial z}{\partial z} = \frac{\partial \bar z}{\partial \bar z} = 1\ .
		\end{align}
		\item<+-> \bluebox{形式上}，$\partial_z, \partial_{\bar z}$ 跟「偏导数」完全一样：接受它们作为偏导存在，$z, \bar z$ \bluebox{形式上独立}
		\item<+-> 通常：$f$ 满足 CR 条件/解析性/全纯性 $\Leftrightarrow$ $f$ \redbox{不含} $\bar z$ $\Leftrightarrow$ $\partial_{\bar z} f = 0$
		\commentblock{奇点}{
		在 \bluebox{奇点} 处，$f$ 只依赖 $z$ $\not\Rightarrow$ $\partial_{\bar z} f = 0$。
		}
		
	\end{itemize}
\end{frame}





\begin{frame}{}
	\frametitle{复导数}
	\transfade
	\begin{itemize}
		\item<+-> 若 $f$ 解析/全纯，则其 \bluebox{复共轭} $\overline{f(z)}$ 满足
		\begin{align}
			\partial_z \overline{f(z)} = 0\ .
		\end{align}
		这样的函数称为 \greenbox{反全纯 (anti-holomorphic) 函数}。
		\item<+-> 一般的复变函数既不是全纯也不是反全纯函数。可以用 $f(z, \bar z)$ 来标明/强调。
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 区域 $D$ 上解析的函数 $f = u + i v$ 的实部和虚部满足 CR 条件
		\begin{align}
			\frac{\partial u}{\partial x} = \frac{\partial v}{\partial y}, \qquad
			\frac{\partial u}{\partial y} = - \frac{\partial v}{\partial x} \ .
		\end{align}
		\item<+-> 对这些方程两边作 $\partial_x$ 和 $\partial_y$
		\begin{align}
			\partial_x: \frac{\partial^2 u}{\partial x^2} = & \ \frac{\partial^2 v}{\partial x\partial y}, 
			& \frac{\partial^2 u}{\partial x\partial y} = & \ - \frac{\partial^2 v}{\partial x^2} \\
			\partial_y: \frac{\partial^2 u}{\partial y\partial x} = & \ \frac{\partial^2 v}{\partial y^2}, 
			&\frac{\partial^2 u}{\partial y^2} = & \ - \frac{\partial^2 v}{\partial y\partial x} \ .
		\end{align}
		\item<+-> 稍作比较可得两条 \greenbox{调和 (harmonic) 方程}
		\begin{align}
			\frac{\partial^2 u}{\partial x^2} + \frac{\partial^2 u}{\partial y^2} = 0, \qquad
			\frac{\partial^2 v}{\partial x^2} + \frac{\partial^2 v}{\partial y^2} = 0
		\end{align}
		
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 解析函数的实部和虚部均为 \greenbox{调和函数}，而且二者由 CR 条件相互锁定，形成一对 \greenbox{共轭调和函数}。
		\item<+-> 从一个调和函数 $u$ (或 $v$) 出发，可以 \bluebox{通过 CR 条件} 解出与之共轭的调和同伴 $v$ (或 $u$)，并由此获得 \bluebox{解析函数}。
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数：例子}
	\transfade
	\begin{itemize}
		\item<+-> 设 $u(x,y) = x^2 - y^2$。若 $v(x,y)$ 是是与之共轭的调和函数，则必然有
		\begin{align}
			dv
			= & \ \frac{\partial v}{\partial x}dx + \frac{\partial v}{\partial y} dy
			= - \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy \\
			= & \ + 2y dx + 2x dy = d(2 xy) \ .
		\end{align}
		\item<+-> 于是 $v = 2 xy + C$，而 $C$ 是 \greenbox{任意实常数}。
		\item<+-> 从而 $f(z) = x^2 - y^2 + 2i xy + Ci = (x + i y)^2 + C i $ 是 \bluebox{解析函数}：
		\begin{align}
			f = z^2 + C \highlight{titleblue}{i} \ .
		\end{align}
	\end{itemize}
\end{frame}




\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 区域 $D$ 中共轭调和函数的 \bluebox{线积分} 求解法：
		\begin{align}
			& \ dv
			= \frac{\partial v}{\partial x}dx + \frac{\partial v}{\partial y} dy
			= - \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy \\
			\Rightarrow & \ v = \int_{(x_0, y_0)}^{(x,y)} dv + C
			= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
		\end{align}
		\commentblock{路径无关性}{
		上述积分只指定了初始点 $(x_0, y_0)$ 和终末点 $(x, y)$。
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 区域 $D$ 中共轭调和函数的 \bluebox{线积分} 求解法：
		\begin{align}
			v = \int_{(x_0, y_0)}^{(x,y)} dv + C
			= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
		\end{align}
		\commentblock{路径无关性}{
		\redbox{具体路径怎么选？}
		\begin{figure}
			\includegraphics{image/three-paths.pdf}
		\end{figure}
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 区域 $D$ 中共轭调和函数的线积分求解法：
		\begin{align}
			v = \int_{(x_0, y_0)}^{(x,y)} dv + C
			= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
		\end{align}
		\commentblock{路径几乎无关性}{
		绿色积分 $-$ 紫色积分 $=$ 闭合路径 $C$ 积分：
		\begin{align}
			& \ \oint_C \left[\highlight{titleblue}{- \frac{\partial u}{\partial y}}dx + \highlight{orange}{\frac{\partial u}{\partial x}}dy\right] =
			\oint [\highlight{titleblue}{P} dx + \highlight{orange}{Q} dy] \\
			\eqtext{Green's} & \ \int \left[
			\frac{\partial}{\partial x} Q - \frac{\partial}{\partial y}P 
			\right]dx dy
			= \int \left[
			\frac{\partial^2 u}{\partial x^2} +
			\frac{\partial^2 u}{\partial y^2}
			\right]dx dy = 0 \ .
		\end{align}
		}
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 区域 $D$ 中共轭调和函数的线积分求解法：
		\begin{align}
			v = \int_{(x_0, y_0)}^{(x,y)} dv + C
			= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
		\end{align}
		\commentblock{路径几乎无关性}{
		蓝色积分 $-$ 绿色积分 $=$ 与 $(x, y), (x_0, y_0)$ 无关的常数 $C$：不要紧。

		\vspace{1em}

		结论：随便选一条连接 $(x_0, y_0)$ 和 $(x,y)$ 的路径，只要 \bluebox{完全在解析区内}。
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 调和方程
		\begin{align}
			\frac{\partial^2 u}{\partial x^2} + \frac{\partial^2 u}{\partial y^2}
			= \left[\frac{\partial^2}{\partial x^2} + \frac{\partial^2}{\partial y^2}\right] u = 0 \ .
		\end{align}
		\item<+-> \bluebox{定理}：当 $u$ 足够光滑，
		\begin{align}
			\partial_z \partial_{\bar z} u = \frac{1}{4} \left[\frac{\partial^2}{\partial x^2} + \frac{\partial^2}{\partial y^2}\right]u \ .
		\end{align}
		\expositionblock{证明}{
		直接利用复导数的定义，以及 $\partial_x \partial_y = \partial_y \partial_x$。
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> $\partial_z \partial_{\bar z} u = 0$ 的 \bluebox{实} 解：
		\begin{align}
			u = \frac{1}{2} \left(f(z) + \overline{f(z)}\right) \ .
		\end{align}
		\expositionblock{解}{
		显然
		\begin{equation}
			\partial_z [\partial_{\bar z} f(z)] = 0 , \qquad
			\partial_z \partial_{\bar z} \overline{f(z)} = \partial_{\bar z}[\partial_z \overline{f(z)}] = 0 \ .
		\end{equation}
		}
		\item<+-> 与 $u$ 共轭的调和函数：$v = \frac{1}{2i} (f(z) - \overline{f(z)}) + C$.
		\commentblock{重新组合}{
		把 $u$ 和 $v$ 重新组合
		\begin{equation}
			u + i v
			= \frac{1}{2}(f(z) + \overline{f(z)})
			+ \frac{1}{2i}i (f(z) - \overline{f(z)})
			= f(z) \ .
		\end{equation}
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数与静电场}
	\transfade
	\begin{itemize}
		\item<+-> 解析函数 $f$ 或者共轭调和函数 $(u, v)$ 可以用于描述 \bluebox{静电场}
		\item<+-> $u, v$ 的 \bluebox{梯度}
		\begin{align}
			\nabla u = \left(\frac{\partial u}{\partial x}, \frac{\partial u}{\partial y}\right), \qquad
			\nabla v = \left(\frac{\partial v}{\partial x}, \frac{\partial v}{\partial y}\right) \ .
		\end{align}
		\item<+-> $\nabla u \perp$ $u$ 等值线, $\nabla v \perp$ $v$ 等值线。
		\begin{figure}
			\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt        

			\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.5]
			%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300

			%Shape: Polygon Curved [id:ds3736050678958065] 
			\draw   (127,103) .. controls (158.24,65.4) and (235.24,53.4) .. (235.24,129.4) .. controls (235.24,205.4) and (289.24,178.4) .. (309.24,208.4) .. controls (329.24,238.4) and (147.24,272.4) .. (127.24,242.4) .. controls (107.24,212.4) and (95.76,140.6) .. (127,103) -- cycle ;
			%Shape: Polygon Curved [id:ds47258583745728866] 
			\draw   (131.24,130.4) .. controls (138.24,92.4) and (216.24,72.4) .. (216.24,148.4) .. controls (216.24,224.4) and (252.24,181.4) .. (272.24,211.4) .. controls (292.24,241.4) and (151.24,238.4) .. (138.24,208.4) .. controls (125.24,178.4) and (124.24,168.4) .. (131.24,130.4) -- cycle ;
			%Shape: Polygon Curved [id:ds7798684470456769] 
			\draw   (145.24,155.4) .. controls (152.24,117.4) and (197.24,136.4) .. (198.24,163.4) .. controls (199.24,190.4) and (205.24,187.4) .. (228.24,212.4) .. controls (251.24,237.4) and (175.24,212.4) .. (167.24,207.4) .. controls (159.24,202.4) and (138.24,193.4) .. (145.24,155.4) -- cycle ;
			%Straight Lines [id:da6892931376631548] 
			\draw    (108,181) -- (50.38,183.38) ;
			\draw [shift={(48.38,183.47)}, rotate = 357.63] [color={rgb, 255:red, 0; green, 0; blue, 0 }  ][line width=0.75]    (10.93,-3.29) .. controls (6.95,-1.4) and (3.31,-0.3) .. (0,0) .. controls (3.31,0.3) and (6.95,1.4) .. (10.93,3.29)   ;
			%Straight Lines [id:da6347469313644816] 
			\draw    (172,74.67) -- (161.52,31.74) ;
			\draw [shift={(161.05,29.8)}, rotate = 76.29] [color={rgb, 255:red, 0; green, 0; blue, 0 }  ][line width=0.75]    (10.93,-3.29) .. controls (6.95,-1.4) and (3.31,-0.3) .. (0,0) .. controls (3.31,0.3) and (6.95,1.4) .. (10.93,3.29)   ;
			%Straight Lines [id:da3694999736877975] 
			\draw    (309.24,208.4) -- (351.6,179.72) ;
			\draw [shift={(353.25,178.6)}, rotate = 145.9] [color={rgb, 255:red, 0; green, 0; blue, 0 }  ][line width=0.75]    (10.93,-3.29) .. controls (6.95,-1.4) and (3.31,-0.3) .. (0,0) .. controls (3.31,0.3) and (6.95,1.4) .. (10.93,3.29)   ;
			%Straight Lines [id:da5424257637203831] 
			\draw    (202,105.67) -- (227.01,74.17) ;
			\draw [shift={(228.25,72.6)}, rotate = 128.45] [color={rgb, 255:red, 0; green, 0; blue, 0 }  ][line width=0.75]    (10.93,-3.29) .. controls (6.95,-1.4) and (3.31,-0.3) .. (0,0) .. controls (3.31,0.3) and (6.95,1.4) .. (10.93,3.29)   ;

			% Text Node
			\draw (50.38,186.47) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle \nabla u$};
			% Text Node
			\draw (150.38,105.47) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle u=u_{1}$};
			% Text Node
			\draw (247.38,150.47) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle u=u_{2}$};
			% Text Node
			\draw (257.38,75.47) node [anchor=north west][inner sep=0.75pt]   [align=left] {$\displaystyle u_{2}  >u_{1}$};


			\end{tikzpicture}
		\end{figure}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数与静电场}
	\transfade
	\begin{itemize}
		\item<+-> \bluebox{定理}：共轭调和条件 $\Rightarrow$ 正交梯度 $\nabla u \cdot \nabla v = 0$
		\expositionblock{正交梯度}{
		直接计算
		\begin{align}
			\nabla u \cdot \nabla v
			= \frac{\partial u}{\partial x}\highlight{titlegreen}{\frac{\partial v}{\partial x}}
			+ \frac{\partial u}{\partial y} \highlight{orange}{\frac{\partial v}{\partial y}}
			= - \frac{\partial u}{\partial x}\highlight{titlegreen}{\frac{\partial u}{\partial y}}
			+ \frac{\partial u}{\partial y}\highlight{orange}{\frac{\partial u}{\partial x}}
			= 0 \ .
		\end{align}
		}
		\item<+-> \bluebox{推论}：等 $u$ 线 $\perp$ 等 $v$ 线。
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数与静电场}
	\transfade
	\begin{itemize}
		\item<+-> 在无电荷分布二维静电场中，电势是平面空间的调和函数
		\expositionblock{二维电势}{
		二维无电荷麦克斯韦方程
		\begin{align}
			\nabla \cdot \vec E(x,y) = 0\ . 
		\end{align}
		然而电势与电场强度的关系是 $\vec E = - \nabla \varphi$，
		\begin{align}
			\Rightarrow \nabla \cdot \nabla \varphi
			= \frac{\partial^2 \varphi}{\partial x^2} + \frac{\partial^2 \varphi}{\partial y^2} = 0 \ .
		\end{align}
		}
	\end{itemize}
\end{frame}
