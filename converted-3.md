### 复数的四则运算：例子

* 考虑两个 **<orange>虚部为零** 的复数 $z_a \coloneqq a + 0 i$，$z_b \coloneqq b + 0 i$，其中 $a, b \in \mathbb{R}$
  \begin{align}
  z_a + z_b = (a + b) + (0 + 0) i = a + b \in \mathbb{R} \ .
  \end{align}

<div class='proof comment'>

**实数与复数的加法**

由此可见，虚部为零的复数加法跟实数加法是一模一样的。
</div>

---

### 复数的四则运算：例子

* 考虑一个虚部为零的复数 (其实就是实数) $z_a \coloneqq a + 0 i$，一个纯虚数 $z_b \coloneqq 0 + bi$，则它们的和
  \begin{align}
  z_a + z_b = (a + 0) + (0 + b)i = a + b i \in \mathbb{C}\ .
  \end{align}

<div class='proof comment'>

**复数的分解**

任何一个复数都可以分解为一个 **<orange>实数** 和一个 **<orange>纯虚数** 的和。
</div>

---

### 复数的代数运算

* 理解这些四则运算有两种思路。

* (1) 把这些 **<orange>具体的式子** 作为加减乘除运算的 **<orange>定义**，可以验证它们满足之前列举的所有性质 (交换律、结合律、分配律等)

* (2) 把运算应满足的 **<orange>规律** (交换律、结合律等) 作为出发点、**<orange>定义**，则上面这些具体的运算规则可以被推导出来，从而获得复数及其运算。

---

### 复数的代数运算

<div class='proof comment'>

**定义对象的新思路**

"It is **<red>not** who I am underneath, but **<orange>what I do**，that defines me."

——著名慈善家、企业家 Bruce Wayne
</div>

<center>

![width:720px](image/Bruce.jpg)
</center>

---

### 复数的代数运算

* 复数乘法定义实际上就是 **<orange>分配律**、**<orange>交换律**、**<orange>结合律** 的结果：把 $a_{1,2} + b_{1,2} i$ 均看成是 **<orange>实数** **<orange>加** **<orange>纯虚数**，
  \begin{align}
  (a_1 + b_1 i) (a_2 + b_2 i)
  = & \
  a_1 a_2 + b_1 i b_2 i + a_1 b_2 i + b_1 i a_2 \\
  = & \ a_1 a_2 + b_1 b_2 \orange{i^2} + (a_1 b_2 + b_1 a_2) i \\
  = & \ a_1 a_2 \orange{-} b_1 b_2 + (a_1 b_2 + b_1 a_2) i
  \end{align}

---

### 复数的代数运算

* $z$ 的模方 (modular-square)
  \begin{align}
  z \bar z = z z^* = (x + i y)(x - i y) = x^2 + y^2 \ .
  \end{align}

<div class='proof comment'>

**因式分解**

$x^2 + y^2$ 可以在复数范围内作因式分解：原本不可能的事情，在复数范围内变得可能。
</div>

* **<green>幂次运算**：对于 $n = 0, 1,2, 3, \dots$，
  \begin{equation}
  z^n = z \cdot z \cdot ... \cdot z \ .
  \end{equation}
* 常用结果：$i^{2k} = (-1)^k$，$i^{4k} = 1$, $k \in \mathbb{N}$

---

### 复数的表示

* **<orange>代数式表示/定义**：对任意 $z \in \mathbb{C}$，存在 $x, y \in \mathbb{R}$ 使得
  \begin{align}
  z = x + y i \ .
  \end{align}
* **<orange>矢量表示**：对任意 $z = x + yi \in \mathbb{C}$，可以在 $\mathbb{R}^2$ 上找到一个点/矢量 $(x,y)$ 对应

  允许矢量平移，矢量加减法 $=$ 复数加减法

<center>

![width:320px](image/vector-rep.jpg)
</center>

---

### 复数的表示

* **<orange>三角表示法**：用极坐标来标记矢量表示法中的 $x,y$，
  \begin{align}
  x = r \cos \theta, \quad y = r \sin \theta \qquad \Rightarrow
  \qquad
  z = & \ r \cos \theta + i r \sin \theta\\
  = & \ r(\cos\theta + i \sin \theta)\ .
  \end{align}
  角度 $\theta \in \mathbb{R}$ 称为 **<green>辐角 $\operatorname{arg} z$**，长度 $r$ 称为 **<green>模 (modulus) $|z|$**，也称为绝对值。
  \begin{align}
  r = |z| = \sqrt{x^2 + y^2} \ .
  \end{align}

<center>

![width:240px](image/Trig-Rep.png)
</center>

---

### 复数的表示

* **<orange>指数表示法**：**<green>$e^{i\theta} \coloneqq \cos \theta + i \sin \theta$**，从而
  \begin{align}
  z = r e^{i \theta} = r \exp \left[i \theta\right] \ .
  \end{align}

<center>

![width:400px](image/Exponential_Rep.png)
</center>

---

### 复数的表示

<div class='proof comment'>

**纯虚数的指数的理解**

注意 $\theta$ 是 **<orange>实数**，$i \theta$ 是 **<orange>纯虚数**。$i\theta$ 的指数应该通过 Taylor 展开，
</div>

\begin{align}
e^{i \theta} = \sum_{n = 0}^{+\infty} \frac{1}{n!} (i \theta)^n
= & \ \sum_{k = 0}^{+\infty} \frac{1}{(2k)!} (i \theta)^{2k}
+ \sum_{k = 0}^{+\infty} \frac{1}{(2k + 1)!} (i \theta)^{2k + 1}
\nonumber\\
= & \ \sum_{k = 0}^{+\infty} \frac{1}{(2k)!} i^{2k} \theta^{2k}
+ \sum_{k = 0}^{+\infty} \frac{1}{(2k + 1)!} i^{2k + 1} \theta^{2k + 1}\nonumber\\
= & \ \sum_{k = 0}^{+\infty} \frac{1}{(2k)!} (-1)^k  \theta^{2k}
+ i \sum_{k = 0}^{+\infty} \frac{1}{(2k + 1)!} (-1)^k \theta^{2k + 1}\nonumber\\
= & \ \cos \theta + i \sin \theta \ .
\end{align}

---

### 复数的表示：辐角多值性

* 三角表示和指数表示中都涉及 **<orange>辐角**。
* 给定一个非零 $z$，其辐角 **<red>不唯一**。对任意 **<green>整数 $k$**，都有
  \begin{align}
  z = r (\cos \theta + i \sin \theta)
  = r (\cos (\theta + 2\pi k) + i \sin (\theta + 2\pi k)) \ .
  \end{align}
  因此，指数表达式也不唯一，
  \begin{align}
  z = re^{i \theta} = r e^{i (\theta + 2\pi k)} \ .
  \end{align}
* 有时候，人们会 **<orange>人为限制** $\theta$ 的一个取值范围，如 $\theta \in [0, 2\pi)$

---

### 复数的表示：例子

* $z = 3 + 4 i$，则利用 $r = \sqrt{3^2 + 4^2} = 5$, $\tan \theta = 4/3$
  \begin{align}
  z = & \ 5 \cos \left(\operatorname{arctan}\frac{4}{3}\right)
  + 5 \sin \left(\operatorname{arctan}\frac{4}{3}\right) i\\
  = & \ 5 \exp \left(i \arctan \frac{4}{3}\right) \ .
  \end{align}

<center>

![height:240px](image/example-1.pdf)
</center>

---

### 复数的表示：例子

* $z = 0 + 3i$，则
  \begin{align}
  z = 3 \cos \frac{\pi}{2} + 3 i \sin \frac{\pi}{2} = 3 e^{i \frac{\pi}{2}} \ .
  \end{align}

---

### 复数的表示：特殊值

* $-1$ 的四种写法
  \begin{align}
  -1 = - 1 + 0 i  =  \cos \pi + i \sin \pi = e^{\pi i} \ .
  \end{align}
  移项：**<orange>欧拉公式**
  \begin{align}
  e^{\pi i} + 1 = 0 \ .
  \end{align}
* $+1$
  \begin{align}
  e^{2\pi i} = \cos 2\pi + i \sin 2\pi = 1 + 0 i = 1\ .
  \end{align}

---

### 复数的表示：特殊值

* 虚数单位 $i$，
  \begin{align}
  i = 0 + i = \cos \frac{\pi}{2} + \sin \frac{\pi}{2} i = e^{ i \frac{\pi}{2}}  \ .
  \end{align}
* 负虚数单位 $- i$，
  \begin{align}
  - i = 0 - i = \cos ( - \frac{\pi}{2}) + \sin (-\frac{\pi}{2}) i = e^{ - i \frac{\pi}{2}} \ .
  \end{align}