### 有理数

* 有理数 (rational numbers)，$\mathbb{Q}$
  $$
  - \frac{3}{4}, \quad \frac{2021}{2022}, \quad \frac{2333}{666}, \quad - 3.1415926, \quad 4 , \quad \ldots \ .
  $$
* 古希腊和印度科学家对有理数进行研究

---

### 无理数

* 无理数 (irrational numbers)
  $$
  e, \qquad \pi, \qquad \sqrt{2}, \qquad \sqrt{3}, \qquad \ldots
  $$

---

### 无理数

* 毕达哥拉斯学派的数学家 Hippasus 提出 $\sqrt{2}$ 是一种之前没有研究过的数：**<orange>无理数**。毕达哥拉斯认为「数是绝对的、万物皆数 (all is number)」，拒绝接受无理数的存在，对 Hippasus 处以 **<red>死刑** (推海里淹死)。

<center>

![height:320px](image/Hippasus.png)
</center>

* 公元前一世纪左右，《九章算术》记载了开方运算中出现的无理数

---

### 无理数

* 在 16 世纪的欧洲，人们逐渐接受 **<orange>负数**、**<orange>分数**，到 18、19 世纪人们以 **<green>代数数** 和 **<green>超越数** 两种角度思考也研究无理数。

<div class='proof comment'>

**代数数**

即整系数多项式的根。
</div>

18, 19世纪数学家 Lambert、Legendre、Weierstrass、Cantor、Dedekind 等都对无理数进行深入的研究

---

### 实数

* 实数 (real numbers), $\mathbb{R}$：有理数与无理数的并集
* 中世纪时，阿拉伯数学家提出实数的概念
* 「实 (real)」来自 17 世纪的笛卡尔

<div class='proof comment'>

**实根与虚根**

17 世纪的数学家已经在研究代数方程根，分为实根 (real roots) 和虚根 (imaginary roots)
</div>

---

### 实数

* 有理数和无理数密密麻麻 **<orange>无缝** 形成实数轴 (real line)

<center>

![width:560px](image/real.png)
</center>

<div class='proof comment'>

**拓扑**

"密密麻麻、无缝"是一种拓扑概念
</div>

---

<!-- _class: lead -->
<!-- _backgroundColor: #f4c2c2 -->

# 二

## 复数

---

### 实数

* 实数及其加、乘运算满足基本性质：对任意 $a, b \in \mathbb{R}$
  * 加法 **<orange>交换** 律 $a + b = b + a$，**<orange>结合** 律 $(a + b) + c = a + (b + c)$
  * 乘法 **<orange>交换** 律 $a \cdot b = b a$，**<orange>结合** 律 $(a \cdot b) \cdot c = a \cdot (b \cdot c)$
  * 加乘 **<orange>分配** 律：$a \cdot (b + c) = a b + a c$
  * 有加法 **<green>单位元** $0 \in \mathbb{R}$：$0 + a = 0 + a = a$
  * 有乘法 **<green>单位元** $1 \in \mathbb{R}$：$1 \cdot a = a \cdot 1 = a$
  * 有乘法 **<orange>零** 元 $0$：$a \cdot 0 = 0 \cdot a = 0$
  * 存在加法 **<green>逆元** $- a$：$(-a) + a = 0 $
  * 当 $a \ne 0$，存在乘法 **<green>逆元** $a^{-1}$：$a^{-1} \cdot a = 1 $

---

### 复数

* 问题一：是否有别的非实数的东西满足上面这些运算性质？
* 问题二：非常简单的实 (整) 系数代数方程 $x^2 + 1 = 0$ 是否有解？
* 目标：扩充实数，使得
  * A: 维持实数的代数性质
  * B: 使得实方程 $x^2 + 1 = 0$ 有解

---

### 复数

* 定义 **<green>虚数单位 (imaginary unit) $i$**，实现目标 B

<div class='proof equation'>

**虚数单位**

$$
i^2 + 1 = 0 \ .
$$
</div>

<div class='proof comment'>

**虚数单位**

有的文献和编程语言使用 $j$ 或者 $I$ 代替 $i$，
</div>

---

### 复数

* 定义 **<green>复数 (complex numbers)** 为所有形如 $a + bi$ 的物体，其中 $a, b \in \mathbb{R}$
* 对任意复数 $z = a + b i$，定义 **<green>实部 $\operatorname{Re}z \coloneqq a$**，**<green>虚部 $\operatorname{Im}z \coloneqq b$**
* 全体复数形成的集合称为 **<green>复数集 $\mathbb{C}$**

---

### 复数的例子

* **<orange>虚部为零** 的复数 $a + 0 i \in \mathbb{C}$ ($a \in \mathbb{R}$) 往往 **<orange>简写** 为 $a \coloneqq a + 0 i$。

<div class='proof comment'>

**子集关系**

实数集可以看成复数集的子集，$\mathbb{R} \subset \mathbb{C}$
</div>

* **<orange>实部为零** 的复数 $0 + a i $ (其中 $a \in \mathbb{R}$) 的数称为 **<green>纯虚 (pure imaginary) 数**
* 虚数单位 $0 + 1 i = i \in \mathbb{C}$
* $0 - 1 i = - i \in \mathbb{C}$
* 纯虚数 **<orange>简写** $0 + a i = a i \in \mathbb{C}$, $\forall a \in \mathbb{R}$

---

### 复数的例子

* 对任意 $z = a + b i$ (其中 $a, b \in \mathbb{R}$)，定义其 **<green>复共轭 (complex conjugate)**
  $$
  \bar z = z^* = (a + bi)^* \coloneqq a - b i \ .
  $$
  读作 z bar 或者 z star。

---

### 复数的四则运算

* 数的最大价值在于运算
* **<green>加法**：
  $$
  (a_1 + b_1 i) + (a_2 + b_2 i) \coloneqq (a_1 + a_2) + (b_1 + b_2)i
  $$
* **<green>减法**：
  $$
  (a_1 + b_1 i) - (a_2 + b_2 i) \coloneqq (a_1 - a_2) + (b_1 - b_2)i
  $$

---

### 复数的四则运算

* **<green>乘法**：
  $$
  (a_1 + b_1 i) \cdot (a_2 + b_2 i) \coloneqq (a_1 a_2 - b_1 b_2) + (a_1 b_2 + a_2 b_1)i \ .
  $$

<div class='proof comment'>

**减号**

注意结果的实部有个 **<orange>减号**
</div>

* **<green>除法**：
  $$
  \frac{a_1 + b_1 i}{a_2 + b_2 i} \coloneqq \frac{
  a_1 a_2 + b_1 b_2
  }{a_2^2 + b_2^2} + \frac{- a_1 b_2 + a_2 b_1}{a_2^2 + b_2^2} i \ .
  $$
