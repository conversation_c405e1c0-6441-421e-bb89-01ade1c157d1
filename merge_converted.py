#!/usr/bin/env python3
"""
Script to merge all converted-i.md files into a single converted.md file
"""

import os

def merge_converted_files():
    """Merge all converted files into converted.md"""
    
    # Start with the header from converted-1.md
    with open('converted-1.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add content from converted-2.md to converted-13.md
    for i in range(2, 14):
        filename = f'converted-{i}.md'
        if os.path.exists(filename):
            print(f"Adding content from {filename}")
            with open(filename, 'r', encoding='utf-8') as f:
                segment_content = f.read()
            
            # Add separator and content
            content += '\n\n---\n\n' + segment_content
        else:
            print(f"Warning: {filename} not found")
    
    # Write the merged content
    with open('converted.md', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Merged all converted files into converted.md")

if __name__ == "__main__":
    merge_converted_files()
