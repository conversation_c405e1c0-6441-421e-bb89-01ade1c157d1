#!/usr/bin/env python3
"""
Proper LaTeX to Marp conversion following tex2marp rules exactly
"""

import re
import os

def convert_latex_to_marp_proper(latex_content):
    """Convert LaTeX content to Marp format following rules exactly"""
    
    content = latex_content
    
    # Remove document structure and preamble (but keep frame content)
    content = re.sub(r'\\documentclass.*?\n', '', content)
    content = re.sub(r'\\usepackage.*?\n', '', content)
    content = re.sub(r'\\newcommand.*?\n', '', content)
    content = re.sub(r'\\definecolor.*?\n', '', content)
    content = re.sub(r'\\pgfdeclareimage.*?\n', '', content)
    content = re.sub(r'\\beamertemplatenavigationsymbolsempty.*?\n', '', content)
    content = re.sub(r'\\begin\{document\}.*?\n', '', content)
    content = re.sub(r'\\end\{document\}.*?\n', '', content)
    
    # Remove comments but preserve content
    content = re.sub(r'^%.*?\n', '', content, flags=re.MULTILINE)
    
    # Convert frames to slides
    content = re.sub(r'\\begin\{frame\}[^}]*', '---\n', content)
    content = re.sub(r'\\end\{frame\}', '', content)
    
    # Convert frame titles
    content = re.sub(r'\\frametitle\{([^}]+)\}', r'### \1', content)
    
    # Remove transition commands
    content = re.sub(r'\\transfade\s*', '', content)
    
    # Convert itemize environments
    content = re.sub(r'\\begin\{itemize\}\s*', '', content)
    content = re.sub(r'\\end\{itemize\}\s*', '', content)
    
    # Convert items - CRITICAL: follow rules exactly
    content = re.sub(r'\\item<\+->\\s*', '* ', content)
    content = re.sub(r'\\item<\+->\\s*', '* ', content)  # Double check
    content = re.sub(r'\\item\\s*', '- ', content)
    
    # Convert colored boxes - CRITICAL: follow rules exactly
    content = re.sub(r'\\bluebox\{([^}]+)\}', r'**<orange>\1**', content)
    content = re.sub(r'\\greenbox\{([^}]+)\}', r'**<green>\1**', content)
    content = re.sub(r'\\redbox\{([^}]+)\}', r'**<red>\1**', content)
    
    # Convert math highlighting - CRITICAL: follow rules exactly
    content = re.sub(r'\\highlight\{red\}\{([^}]+)\}', r'\\red{\1}', content)
    content = re.sub(r'\\highlight\{titlegreen\}\{([^}]+)\}', r'\\green{\1}', content)
    content = re.sub(r'\\highlight\{titleblue\}\{([^}]+)\}', r'\\orange{\1}', content)
    
    # Convert comment blocks - CRITICAL: follow rules exactly with proper spacing
    def convert_commentblock(match):
        title = match.group(1)
        content_text = match.group(2).strip()
        # Apply color box conversions within comment content
        content_text = re.sub(r'\\bluebox\{([^}]+)\}', r'**<orange>\1**', content_text)
        content_text = re.sub(r'\\greenbox\{([^}]+)\}', r'**<green>\1**', content_text)
        content_text = re.sub(r'\\redbox\{([^}]+)\}', r'**<red>\1**', content_text)
        return f"\n<div class='proof comment'>\n\n**{title}**\n\n{content_text}\n</div>\n"
    
    content = re.sub(r'\\commentblock\{([^}]+)\}\{([^}]+)\}', convert_commentblock, content)
    
    # Convert exposition blocks
    def convert_expositionblock(match):
        title = match.group(1)
        content_text = match.group(2).strip()
        return f"\n<div class='proof'>\n\n**{title}**\n\n{content_text}\n</div>\n"
    
    content = re.sub(r'\\expositionblock\{([^}]+)\}\{([^}]+)\}', convert_expositionblock, content)
    
    # Convert equation blocks (custom command)
    def convert_equationblock(match):
        title = match.group(1)
        equation = match.group(2).strip()
        return f"\n<div class='proof equation'>\n\n**{title}**\n\n{equation}\n</div>\n"
    
    content = re.sub(r'\\equationblock\{([^}]+)\}\{([^}]+)\}', convert_equationblock, content)
    
    # Convert section titles with proper Marp format
    def convert_section_title(match):
        full_match = match.group(0)
        # Extract section number and title from the complex LaTeX structure
        section_num_match = re.search(r'\\Huge\{\\color\{white\}\{\\textbf\{\{([^}]+)\}\}\}\}', full_match)
        section_title_match = re.search(r'\\huge\{\\color\{white\}\{\\textbf\{\{([^}]+)\}\}\}\}', full_match)
        
        if section_num_match and section_title_match:
            section_num = section_num_match.group(1)
            section_title = section_title_match.group(1)
            return f"\n<!-- _class: lead -->\n<!-- _backgroundColor: #f4c2c2 -->\n\n# {section_num}\n\n## {section_title}\n"
        return ""
    
    content = re.sub(r'\{\\setbeamertemplate\{background\}.*?\\end\{frame\}\s*\}', convert_section_title, content, flags=re.DOTALL)
    
    # Convert images - follow rules exactly
    def convert_image(match):
        options = match.group(1)
        filename = match.group(2)
        
        # Extract width information
        width_match = re.search(r'width=([0-9.]+)\\textwidth', options)
        height_match = re.search(r'height=([0-9.]+)\\textheight', options)
        
        if width_match:
            width_factor = float(width_match.group(1))
            # Convert to pixels (assuming slide width ~800px)
            width_px = int(width_factor * 800)
            return f"![width:{width_px}px]({filename})"
        elif height_match:
            height_factor = float(height_match.group(1))
            # Convert to pixels (assuming slide height ~600px)
            height_px = int(height_factor * 600)
            return f"![height:{height_px}px]({filename})"
        else:
            return f"![]({filename})"
    
    content = re.sub(r'\\includegraphics\[([^]]+)\]\{([^}]+)\}', convert_image, content)
    
    # Convert figure environments
    content = re.sub(r'\\begin\{figure\}\s*', '<center>\n\n', content)
    content = re.sub(r'\\end\{figure\}\s*', '\n</center>', content)
    content = re.sub(r'\\centering\s*', '', content)
    content = re.sub(r'\\begin\{center\}\s*', '<center>\n\n', content)
    content = re.sub(r'\\end\{center\}\s*', '\n</center>', content)
    content = re.sub(r'\\caption\{([^}]+)\}\s*', r'\1', content)
    
    # Handle rightline and other LaTeX commands
    content = re.sub(r'\\rightline\{([^}]+)\}', r'\1', content)
    
    # CRITICAL: Keep align environments as-is (DO NOT CHANGE)
    # The rules specifically say to keep \begin{align}...\end{align} and alignment points
    
    # Clean up extra whitespace and formatting
    content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
    content = re.sub(r'^\s+', '', content, flags=re.MULTILINE)
    content = re.sub(r'\s+$', '', content, flags=re.MULTILINE)
    
    # Remove any remaining LaTeX artifacts
    content = re.sub(r'\\\s*$', '', content, flags=re.MULTILINE)
    
    return content.strip()

def convert_segment(segment_num):
    """Convert a single segment"""
    input_file = f"to-convert-{segment_num}.tex"
    output_file = f"converted-{segment_num}.md"
    
    if not os.path.exists(input_file):
        print(f"Warning: {input_file} not found")
        return False
        
    print(f"Converting {input_file} to {output_file}")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        latex_content = f.read()
    
    marp_content = convert_latex_to_marp_proper(latex_content)
    
    # For segment 1, add the Marp header
    if segment_num == 1:
        header = """---
marp: true
theme: rose-pine-dawn
paginate: true
_paginate: skip
size: 16:9

math: mathjax

---
# 复数简介
$$
\\newcommand\\blue[1]{{\\color[rgb]{0.20, 0.43, 0.75}{#1}}}
\\newcommand\\red[1]{{\\color[rgb]{0.839844, 0.507813, 0.488281}{#1}}}
\\newcommand\\green[1]{{\\color[rgb]{.359375, .59765625, .41015625}{#1}}}
\\newcommand\\gray[1]{{\\color[rgb]{0.5, 0.5, 0.5}{#1}}}
\\newcommand\\purple[1]{{\\color[rgb]{0.63515625, 0.49609375, 0.80859375}{#1}}}
\\newcommand\\white[1]{{\\color{white}{#1}}}
\\newcommand\\orange[1]{{\\color[rgb]{0.63515625, 0.51015625, 0.37734375}{#1}}}
$$

**潘逸文**
*<EMAIL>*

"""
        marp_content = header + marp_content
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(marp_content)
    
    print(f"Successfully converted {input_file} -> {output_file}")
    return True

def main():
    """Convert all segments properly"""
    success_count = 0
    for i in range(1, 14):  # Convert segments 1-13
        if convert_segment(i):
            success_count += 1
    
    print(f"Successfully converted {success_count} segments")

if __name__ == "__main__":
    main()
