\begin{frame}{}
	\frametitle{解析函数}
	\transfade
	\begin{itemize}
		\item<+-> 倘若函数 $f$ 在 \bluebox{区域 $D$} 内处处可导，则称 $f$ 是 $D$ 上的 \greenbox{解析 (analytic) 函数}，或者 \greenbox{全纯 (holomorphic) 函数}，或者称 $f$ 在 $D$ 内 \greenbox{解析}。
		\commentblock{解析}{
		解析这个形容词是针对 \bluebox{一个区域} 的概念，是对函数在 \bluebox{一定范围} 内的行为进行约束，并非一点处的概念，\redbox{没有}「在一点处解析」的说法。

		有时人会 \bluebox{偷懒} 说「在一点处解析」，但实际意思是指在 \bluebox{点的邻域} 内解析。
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{可导性：例}
	\transfade
	\begin{itemize}
		\item<+-> $f(z) = z^n$，$n \in \mathbb{N}$，则可以直接计算得到
		\begin{align}
			\lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z} = n z^{n - 1}\ .
		\end{align}
		因此这个 $f$ 在 $\mathbb{C}$ 上 \bluebox{处处可导}。
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{可导性：例}
	\transfade
	\begin{itemize}
		\item<+-> $f(z) = \bar{z}$。因此 $f(z = x + i y) = x - i y$
		\visible<+->{{\small\begin{align}
					\lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z} = \lim_{\substack{\Delta x \to 0 \\ \Delta y \to 0}} \frac{x + \Delta x - i (y + \Delta y) - (x - i y)}{\Delta x + i \Delta y} \ .
				\end{align}}}

		\visible<+->{若 $\Delta z$ 沿着 \bluebox{实轴} 逼近 $0$，则 $\Delta y = 0$，
				\begin{align}
					\lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z}
					= \lim_{\substack{\Delta x \to 0 \\ \Delta y \to 0}} \frac{\Delta x }{\Delta x} = \highlight{titleblue}{1} \ .
				\end{align}}

		\visible<+->{若 $\Delta z$ 沿着 \bluebox{虚轴} 逼近 $0$，则 $\Delta x = 0$，
				\begin{align}
					\lim_{\Delta z \to 0} \frac{f(z + \Delta z) - f(z)}{\Delta z}
					= \lim_{\substack{\Delta x \to 0 \\ \Delta y \to 0}} \frac{ - i \Delta y }{i \Delta y} = \highlight{red}{- 1} \ .
				\end{align}}

	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{可导性：例}
	\transfade
	\begin{itemize}
		\item<+-> 极限不存在，因此 $f(z) \coloneqq \bar z$ \redbox{处处不可导}。
	\end{itemize}
\end{frame}




\begin{frame}{}
	\frametitle{解析函数}
	\transfade
	\begin{itemize}
		\item<+-> \greenbox{定义}：倘若 $f$ 在 $z_0$ 处不解析 (包括没有定义)，但是 $f$ 在 $z_0$ 的任意邻域内 \bluebox{都有} 解析点，则称 $z_0$ 为 $f$ 的 \greenbox{奇点 (singularity)}。
		\commentblock{奇点的含义}{
		不同领域对奇点的定义和理解会有所不同。这里是复分析中比较常用的一种理解。
		}
		% \item<+-> \greenbox{定义}：设 $D$ 是区域，若 $f$ 在 $D$ 中除了若干孤立的点 \bluebox{以外} 处处解析，有时会强调 $f$ 是 $D$ 上的 \greenbox{亚纯 (meromorphic) 函数}。
		% \commentblock{本课程不区分亚纯全纯}{
		% 本课程后续讨论不会使用亚纯这一术语。
		% }
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{求导法则}
	\transfade
	\begin{itemize}
		\item<+-> 求导法则

		(1) 线性性：$[f(z) + g(z)]' = f'(z) + g'(z)$

		(2) 莱布尼兹 (Leibniz) 律：$[f(z)g(z)]' = f'(z)g(z) + f(z) g'(z)$

		(3) 链式法则 (chain rule)：$f(g(z))' = \frac{df}{dg} \frac{dg}{dz}$
		\item<+-> 常见复解析函数导数与实光滑函数的导数在形式上是一样的。
	\end{itemize}
\end{frame}




\begin{frame}{}
	\frametitle{Cauchy-Riemann 条件}
	\transfade
	\begin{itemize}
		\item<+-> \bluebox{复可导性} 是很 \bluebox{强} 的条件，比之前学的实变可导性要强得多：要求极限与 $\Delta z \to 0$ 的 \bluebox{方向无关}
		\begin{center}
			\includegraphics[height=0.6\textheight]{image/mikasa.jpg}
		\end{center}
	\end{itemize}
\end{frame}




\begin{frame}{}
	\frametitle{Cauchy-Riemann 条件}
	\transfade
	\begin{itemize}
		\item<+-> 求导：$f(z = x + i y) = u(x, y) + i v(x, y)$
		\begin{align}
			f'(z)
			= \lim_{\substack{\Delta x \to 0\\\Delta y \to 0}}\bigg[ & \ \frac{u(x + \Delta x, y + \Delta y) + i v(x + \Delta x, y + \Delta y)}{\Delta x + i \Delta y}\\
			& \ \qquad \qquad\qquad \qquad \qquad - \frac{u(x, y ) + i v(x, y )}{\Delta x + i \Delta y} \bigg] \ . \nonumber
		\end{align}
		\item<+-> 考虑实轴和虚轴方向
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{Cauchy-Riemann 条件}
	\transfade
	\begin{itemize}
		\item<+-> 考虑 $\Delta z$ 从 \bluebox{实轴} 趋近零：\bluebox{$\Delta y = 0$}，
		\begin{align}
			f'(z) = \frac{\partial u}{\partial x} + i \frac{\partial v}{\partial x} \ .
		\end{align}
		\item<+-> 考虑 $\Delta z$ 从 \bluebox{虚轴} 趋近零：\bluebox{$\Delta x = 0$}，
		\begin{align}
			f'(z) = \frac{\partial v}{\partial y} - i \frac{\partial u}{\partial y} \ .
		\end{align}
		\item<+-> 二者应该相等 (方向无关)：\greenbox{Cauchy-Riemann 条件}
		\begin{align}
			& \ \frac{\partial u}{\partial x} + i \frac{\partial v}{\partial x}
			= \frac{\partial v}{\partial y} - i \frac{\partial u}{\partial y}\\
			\Rightarrow & \ \highlight{titlegreen}{
			\frac{\partial u}{\partial x} = \frac{\partial v}{\partial y}, \qquad
			\frac{\partial u}{\partial y} = - \frac{\partial v}{\partial x}} \ .
		\end{align}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{Cauchy-Riemann 条件的复坐标表示}
	\transfade
	\begin{itemize}
		\item<+-> \bluebox{定理}：实可微性 + CR 条件 $\Leftrightarrow$ 复可导性
		\item<+-> \bluebox{定理}：实连续一阶偏导 + CR 条件 $\Leftrightarrow$ 复可导性
		\item<+-> 通常，物理中的函数都至少具有上述 \bluebox{实} 可导性质。以后统称为 \greenbox{足够光滑、性质良好} 的函数。
	\end{itemize}
\end{frame}


\begin{frame}
	\frametitle{Cauchy-Riemann 条件的复坐标表示}
	\commentblock{实可微性}{
	二元函数 $f(x,y)$ 在 $(x_0, y_0)$ 实可微指的是 
	\begin{equation}
		\lim_{\substack{\Delta x \to 0 \\ \Delta y \to 0}} \frac{[f(x + \Delta x, y + \Delta y) - f(x_0, y_0) -  \partial_x f \Delta x - \partial_y f \Delta y]_{(x_0, y_0)}}{\sqrt{\Delta x^2 + \Delta y^2}} = 0 \nonumber
	\end{equation}
	
	偏导数存在但不可微的例子
	\begin{equation}
		\sqrt{xy}, \qquad \left\{\begin{array}{cc}
			0 & (0,0)\\
			\frac{y^3}{x^2 + y^2} & (x, y) \neq (0,0)
		\end{array}\right.
	\end{equation}
	}
	
\end{frame}



\begin{frame}{}
	\frametitle{Cauchy-Riemann 条件的复坐标表示}
	\transfade
	\begin{itemize}
		\item<+-> $z = x + i y$，$\bar z = x - i y$
		\item<+-> 反解
		\begin{align}
			x = \frac{1}{2}(z + \bar z), \qquad
			y = \frac{1}{2i}(z - \bar z) \ .
		\end{align}
		\item<+-> 任何关于 $z, \bar z$ 的函数都可以重新表达为 $x, y$ 的函数，反之亦然，
		\begin{align}
			f(z, \bar z) = & \ f(x + i y, x - i y),\\
			f(x, y) = & \ f\left(\frac{1}{2}(z + \bar z), \frac{1}{2i}(z - \bar z)\right) \ .
		\end{align}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{Cauchy-Riemann 条件的复坐标表示}
	\transfade
	\begin{itemize}
		\item<+-> \greenbox{定义} 复偏导符号 $\frac{\partial}{\partial z}, \frac{\partial}{\partial \bar z}$
		\begin{align}
			\frac{\partial f}{\partial z} = \partial_z f \coloneqq \frac{1}{2} \left(\frac{\partial f}{\partial x} \highlight{titlegreen}{-} i \frac{\partial f}{\partial y}\right) \ ,\\
			\frac{\partial f}{\partial \bar z} = \partial_{\bar z} f \coloneqq \frac{1}{2} \left(\frac{\partial f}{\partial x} \highlight{titlegreen}{+} i \frac{\partial f}{\partial y}\right)\ .
		\end{align}
		\commentblock{链式法则}{
		上述求导算符可以通过「链式法则」帮助理解 (不是证明)，
		\begin{align}
			\frac{\partial f}{\partial z} = \frac{\partial f}{\partial x} \frac{\partial x}{\partial z}
			+ \frac{\partial f}{\partial y} \frac{\partial y}{\partial z}
			= \frac{1}{2} \frac{\partial f}{\partial x} - \frac{i}{2} \frac{\partial f}{\partial y}\\
			\frac{\partial f}{\partial \bar z} = \frac{\partial f}{\partial x} \frac{\partial x}{\partial \bar z}
			+ \frac{\partial f}{\partial y} \frac{\partial y}{\partial \bar z}
			= \frac{1}{2} \frac{\partial f}{\partial x} + \frac{i}{2} \frac{\partial f}{\partial y}
		\end{align}
		}
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{Cauchy-Riemann 条件的复坐标表示}
	\transfade
	\begin{itemize}
		\item<+-> Cauchy-Riemann 条件可以重新组合
		\begin{align}
			& \ \frac{\partial u}{\partial x} - \frac{\partial v}{\partial y} = 0, \qquad
			\frac{\partial u}{\partial y} + \frac{\partial v}{\partial x} = 0 \\
			\Rightarrow & \ \left(\frac{\partial u}{\partial x} - \frac{\partial v}{\partial y}\right)
			+ i \left(\frac{\partial u}{\partial y} + \frac{\partial v}{\partial x}\right) = 0\\
			\Rightarrow & \ \frac{\partial (\highlight{titlegreen}{u + i v})}{\partial x} + i \frac{\partial (\highlight{titlegreen}{u + i v})}{\partial y} = 0 \\
			\Rightarrow & \ \partial_{\bar z} \highlight{titlegreen}{f} = 0 \ .
		\end{align}
		\item<+-> CR 条件 $\Leftrightarrow$ $\partial_{\bar z} f = 0$
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{复导数}
	\transfade
	\begin{itemize}
		\item<+-> 实变函数的偏导数建基于自变量 $\Delta x, \Delta y$ 的 \bluebox{独立性}
		\begin{align}
			\frac{\partial u}{\partial x} = \lim_{\Delta x \to 0} \frac{u(x + \Delta x, y) - u(x,y)}{\Delta x}
		\end{align}
		\item<+-> 特别地，
		\begin{align}
			\frac{\partial x}{\partial y} = \frac{\partial y}{\partial x} = 0, \qquad
			\frac{\partial x}{\partial x} = \frac{\partial y}{\partial y} = 1 \ .
		\end{align}

	\end{itemize}
\end{frame}
